name: Flutter CI/CD Workflow

# Triggers
on:
  push:
    branches:
      - dev     # Development branch
      - main    # Production branch
  pull_request:
    branches:
      - dev
      - main

# Permission configurations
permissions:
  # For uploading artifacts
  actions: write
  # For reading code
  contents: read
  # For publishing test results
  checks: write
  # For adding comments in PR
  pull-requests: write

jobs:
  # Development branch workflow
  dev-workflow:
    if: github.ref == 'refs/heads/dev'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Flutter Setup
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.3'
          channel: 'stable'
      
      - name: Get Dependencies
        run: flutter pub get
      
      # Run tests and generate coverage report
      - name: Run Tests
        run: |
          flutter test --coverage --machine > test-results.json
          flutter pub global activate junitreport
          flutter pub global run junitreport:tojunit --input test-results.json --output test-results.xml
      
      # Upload test reports
      - name: Upload Test Results
        if: always()  # Upload results even if tests fail
        uses: actions/upload-artifact@v4
        with:
          name: dev-test-results
          path: |
            test-results.xml
            coverage/lcov.info
      
      # Publish test reports
      - name: Publish Test Results
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          files: test-results.xml
          # Add token configuration
          github_token: ${{ secrets.GITHUB_TOKEN }}
      
      # Code quality check
      - name: Analyze Code
        run: flutter analyze
        
      # Build development version
      # - name: Build Debug Version
      #   run: |
      #     flutter build apk --debug
      #     flutter build ios --debug --no-codesign
          
      # Upload development artifacts
      # - name: Upload Debug Artifacts
      #   uses: actions/upload-artifact@v3
      #   with:
      #     name: dev-builds
      #     path: |
      #       build/app/outputs/flutter-apk/app-debug.apk
      #       build/ios/iphoneos/Runner.app

  # Production branch workflow
  prod-workflow:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Flutter Setup
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.5'
          channel: 'stable'
      
      - name: Get Dependencies
        run: flutter pub get
      
      # Run tests and generate coverage report
      - name: Run Tests
        run: |
          flutter test --coverage --machine > test-results.json
          flutter pub global activate junitreport
          flutter pub global run junitreport:tojunit --input test-results.json --output test-results.xml
      
      # Upload test reports
      - name: Upload Test Results
        if: always()  # Upload results even if tests fail
        uses: actions/upload-artifact@v4
        with:
          name: prod-test-results
          path: |
            test-results.xml
            coverage/lcov.info
            
      # Publish test reports
      - name: Publish Test Results
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          files: test-results.xml
          # Add token configuration
          github_token: ${{ secrets.GITHUB_TOKEN }}
      
      # Code quality check
      - name: Analyze Code
        run: flutter analyze
      
      # Build Android release version
      - name: Build Android Release
        run: flutter build apk --release
      
      # Build iOS release version
      - name: Build iOS Release
        run: flutter build ios --release --no-codesign
      
      # Upload production artifacts
      - name: Upload Release Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: prod-builds
          path: |
            build/app/outputs/flutter-apk/app-release.apk
            build/ios/iphoneos/Runner.app