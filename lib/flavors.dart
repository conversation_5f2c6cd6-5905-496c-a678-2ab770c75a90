enum Flavor {
  dev,
  staging,
  prod,
}

class F {
  static Flavor? appFlavor;

  static String get name => appFlavor?.name ?? '';

  static String get title {
    switch (appFlavor) {
      case Flavor.dev:
        return 'E2EE Chat DEV';
      case Flavor.staging:
        return 'E2EE Chat STAGING';
      case Flavor.prod:
        return 'E2EE Chat PROD';
      default:
        return 'title';
    }
  }

  // Environment specific configurations
  static String get connectionString {
    switch (appFlavor) {
      case Flavor.dev:
        return 'http://44.206.57.43:7070';
      case Flavor.staging:
        return 'http://44.206.57.43:7070';
      case Flavor.prod:
        return 'http://44.206.57.43:7070';
      default:
        return '';
    }
  }

  static String get websocketUrl {
    switch (appFlavor) {
      case Flavor.dev:
        return 'ws://44.206.57.43:7070/rtc/ws';
      case Flavor.staging:
        return 'ws://44.206.57.43:7070/rtc/ws';
      case Flavor.prod:
        return 'ws://44.206.57.43:7070/rtc/ws';
      default:
        return '';
    }
  }

  static String get envName {
    switch (appFlavor) {
      case Flavor.dev:
        return 'dev';
      case Flavor.staging:
        return 'staging';
      case Flavor.prod:
        return 'prod';
      default:
        return '';
    }
  }

  static String get agoraAppId {
    switch (appFlavor) {
      case Flavor.dev:
        return 'a21d5f28ff594cf5a91d3f1dd08aa45c';
      case Flavor.staging:
        return 'a21d5f28ff594cf5a91d3f1dd08aa45c';
      case Flavor.prod:
        return 'a21d5f28ff594cf5a91d3f1dd08aa45c';
      default:
        return '';
    }
  }

  static String get privacyPolicyUrl {
    switch (appFlavor) {
      case Flavor.dev:
        return 'https://sites.google.com/geekermuse.com/privacypolicy/index';
      case Flavor.staging:
        return 'https://sites.google.com/geekermuse.com/privacypolicy/index';
      case Flavor.prod:
        return 'https://sites.google.com/geekermuse.com/privacypolicy/index';
      default:
        return '';
    }
  }

  static String get termsOfServiceUrl {
    switch (appFlavor) {
      case Flavor.dev:
        return 'https://sites.google.com/geekermuse.com/tos/index';
      case Flavor.staging:
        return 'https://sites.google.com/geekermuse.com/tos/index';
      case Flavor.prod:
        return 'https://sites.google.com/geekermuse.com/tos/index';
      default:
        return '';
    }
  }

  static bool get isProduction => appFlavor == Flavor.prod;
}
