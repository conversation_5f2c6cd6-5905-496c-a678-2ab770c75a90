import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/theme/app_colors.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/purchase/presentation/providers/purchase_process_state_provider.dart';
import 'package:flutter_audio_room/gen/assets.gen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../providers/credit_purchase_provider.dart';
import '../widgets/credit_purchase_item.dart';

class CreditPurchasePage extends ConsumerWidget {
  const CreditPurchasePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final purchaseState = ref.watch(creditPurchaseProvider);
    final processState = ref.watch(purchaseProcessStateProvider);

    // 监听购买状态变化
    ref.listen(purchaseProcessStateProvider, (previous, current) {
      if (previous != current && current == PurchaseProcessState.verified) {
        // 购买验证成功，可以在这里执行额外操作
        LoadingUtils.showToast('Purchase successful!');
      } else if (previous != current &&
          current == PurchaseProcessState.failed) {
        // 购买失败，可以显示重试按钮或其他UI
      } else if (previous != current &&
          current == PurchaseProcessState.canceled) {
        LoadingUtils.showToast('Purchase cancelled');
      }
    });

    // 判断按钮是否应该禁用
    bool isPurchasing() {
      return processState == PurchaseProcessState.purchasing ||
          processState == PurchaseProcessState.verifying;
    }

    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            centerTitle: false,
            title: _buildTitle(context, ref),
          ),
          body: Column(
            children: [
              Expanded(
                child: purchaseState.when(
                  data: (items) {
                    if (items.isEmpty) {
                      return const Center(
                        child: Text('No products available'),
                      );
                    }

                    // 计算每行显示的项目数量
                    final crossAxisCount =
                        MediaQuery.of(context).size.width > 600 ? 3 : 2;

                    return GridView.builder(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: crossAxisCount,
                        crossAxisSpacing: 16.w,
                        mainAxisSpacing: 16.w,
                      ),
                      itemCount: items.length,
                      itemBuilder: (context, index) {
                        final item = items[index];
                        return CreditPurchaseItem(
                          item: item,
                          onPurchase: () {
                            // 如果正在处理购买，禁止点击
                            if (isPurchasing()) {
                              LoadingUtils.showToast('Processing, please wait');
                              return;
                            }
                            ref
                                .read(creditPurchaseProvider.notifier)
                                .purchaseProduct(item.productId);
                          },
                          isDisabled: isPurchasing(),
                        );
                      },
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text(
                          'It seems that there is an error, please try again later',
                          style: TextStyle(color: Colors.red),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            ref.invalidate(creditPurchaseProvider);
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // 显示全屏加载指示器
        if (isPurchasing())
          Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(
                    processState == PurchaseProcessState.purchasing
                        ? 'Processing purchase...'
                        : 'Verifying purchase...',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTitle(BuildContext context, WidgetRef ref) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // 图标容器
        Container(
          height: 30.h,
          width: 30.h,
          alignment: Alignment.center,
          margin: EdgeInsets.only(top: 4.h),
          child: Assets.images.ghostCoin.image(),
        ),
        // 文本容器
        Flexible(
          child: Text.rich(
            TextSpan(
              children: [
                TextSpan(text: '${context.l10n.totalCoins}: '),
                TextSpan(
                  text:
                      ref.watch(accountProvider).wallet?.gems.toString() ?? '0',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18.sp,
                  ),
                )
              ],
            ),
            style: context.textTheme.bodySmallSemiBold.copyWith(
              color: AppColors.ghostCoinColor,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
