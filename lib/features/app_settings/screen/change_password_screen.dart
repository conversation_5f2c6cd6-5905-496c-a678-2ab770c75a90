import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/theme/app_colors.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_scaffold.dart';
import 'package:flutter_audio_room/features/authentication/data/model/contact_info_model.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/authentication/presentation/widgets/auth_button.dart';
import 'package:flutter_audio_room/features/authentication/presentation/widgets/forms/password_form.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Provider for change password contact info
final changePasswordContactInfoProvider =
    FutureProvider.autoDispose<ContactInfoModel?>((ref) async {
  // Call API to get contact info
  final response = await ref.watch(accountProvider.notifier).getContactInfo();

  return response.fold(
    (failure) {
      return null;
    },
    (contactInfo) {
      return contactInfo;
    },
  );
});

class ChangePasswordScreen extends ConsumerStatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  ConsumerState<ChangePasswordScreen> createState() =>
      _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends ConsumerState<ChangePasswordScreen> {
  PasswordFormData? _newPasswordData;
  PasswordFormData? _confirmPasswordData;
  String? _verificationCode;
  bool _isLoading = false;
  bool _isEmailSelected = false;
  bool _codeSent = false;
  bool _passwordSet = false;

  @override
  Widget build(BuildContext context) {
    // Watch contact info state
    final contactInfoAsyncValue = ref.watch(changePasswordContactInfoProvider);
    return AppScaffold(
      appBar: AppBar(
        title: const Text('Change Password'),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    30.verticalSpace,
                    if (!_passwordSet) _buildPasswordSection(),
                    if (_passwordSet)
                      _buildVerificationSection(contactInfoAsyncValue),
                    30.verticalSpace,
                    _buildInfoSection(),
                    20.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.w),
            child: _buildActionButton(contactInfoAsyncValue),
          ),
        ],
      ),
    );
  }

  // Build password input section
  Widget _buildPasswordSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Set New Password',
          style: context.textTheme.headlineSmallBold,
        ),
        16.verticalSpace,
        Text(
          'Please enter your new password:',
          style: context.textTheme.bodyMedium,
        ),
        20.verticalSpace,
        PasswordForm(
          onDataChanged: (data) {
            setState(() {
              _newPasswordData = data;
            });
          },
        ),
        16.verticalSpace,
        PasswordForm(
          onDataChanged: (data) {
            setState(() {
              _confirmPasswordData = data;
            });
          },
        ),
        if (_newPasswordData != null &&
            _confirmPasswordData != null &&
            _newPasswordData!.password.isNotEmpty &&
            _confirmPasswordData!.password.isNotEmpty &&
            _newPasswordData!.password != _confirmPasswordData!.password) ...[
          8.verticalSpace,
          Text(
            'Passwords do not match',
            style: context.textTheme.captionRegular.copyWith(
              color: context.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  // Build verification section
  Widget _buildVerificationSection(
      AsyncValue<ContactInfoModel?> contactInfoAsyncValue) {
    return contactInfoAsyncValue.when(
      data: (contactInfo) {
        if (contactInfo == null || !contactInfo.hasContactInfo) {
          return Center(
            child: Text(
              'No contact information available to verify your identity',
              style: context.textTheme.bodyLargeMedium.copyWith(
                color: AppColors.alert,
              ),
              textAlign: TextAlign.center,
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Verify Identity',
              style: context.textTheme.headlineSmallBold,
            ),
            16.verticalSpace,
            Text(
              'Please select a method to receive verification code:',
              style: context.textTheme.bodyMedium,
            ),
            20.verticalSpace,
            _buildContactSelector(contactInfo),
            20.verticalSpace,
            if (_codeSent) _buildVerificationCodeInput(),
            if (!_codeSent) _buildSendCodeButton(),
          ],
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 48.r),
            16.verticalSpace,
            Text(
              'Failed to get contact information',
              style: context.textTheme.bodyLargeMedium.copyWith(
                color: Colors.red,
              ),
            ),
            8.verticalSpace,
            Text(
              error.toString(),
              style: context.textTheme.captionMedium.copyWith(
                color: AppColors.alert,
              ),
              textAlign: TextAlign.center,
            ),
            16.verticalSpace,
            ElevatedButton(
              onPressed: () => ref.refresh(changePasswordContactInfoProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  // Build contact selector dropdown
  Widget _buildContactSelector(ContactInfoModel contactInfo) {
    List<DropdownMenuItem<bool>> items = [];
    bool onlyOne = false;

    // Check if phone number exists
    if (contactInfo.hasPhone && contactInfo.maskedPhone != null) {
      final phoneDisplay =
          "+${contactInfo.countryNumber ?? ''} ${contactInfo.maskedPhone ?? ''}";
      items.add(DropdownMenuItem(
        value: false,
        child: Row(
          children: [
            Icon(Icons.phone_android,
                color: context.colorScheme.onPrimary, size: 20.r),
            16.horizontalSpace,
            Text(
              'Phone: $phoneDisplay',
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colorScheme.onPrimary,
              ),
            ),
          ],
        ),
      ));
    }

    // Check if email exists
    if (contactInfo.hasEmail && contactInfo.maskedEmail != null) {
      items.add(DropdownMenuItem(
        value: true,
        child: Row(
          children: [
            Icon(Icons.email_outlined,
                color: context.colorScheme.onPrimary, size: 20.r),
            16.horizontalSpace,
            Text(
              'Email: ${contactInfo.maskedEmail}',
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colorScheme.onPrimary,
              ),
            ),
          ],
        ),
      ));
    }

    onlyOne = items.length == 1;
    if (onlyOne) {
      _isEmailSelected = items[0].value ?? false;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 12.r),
      decoration: BoxDecoration(
        color: context.colorScheme.primary,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: onlyOne
          ? items[0].child
          : DropdownButtonHideUnderline(
              child: DropdownButton<bool>(
                isExpanded: true,
                value: _isEmailSelected,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _isEmailSelected = value;
                      _codeSent = false;
                      _verificationCode = null;
                    });
                  }
                },
                dropdownColor: context.colorScheme.primary,
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.colorScheme.onPrimary,
                ),
                icon: Icon(
                  Icons.arrow_drop_down,
                  color: context.colorScheme.onPrimary,
                  size: 24.r,
                ),
                items: items,
              ),
            ),
    );
  }

  // Build send code button
  Widget _buildSendCodeButton() {
    return Center(
      child: Container(
        width: 180.w,
        height: 44.h,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              context.colorScheme.secondary,
              context.colorScheme.secondary.withValues(alpha: 0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: context.colorScheme.secondary.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _sendVerificationCode(_isEmailSelected),
            borderRadius: BorderRadius.circular(12.r),
            child: Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.send_rounded,
                    color: context.colorScheme.onSecondary,
                    size: 18.r,
                  ),
                  8.horizontalSpace,
                  Text(
                    'Send Code',
                    style: context.textTheme.bodyLargeMedium.copyWith(
                      color: context.colorScheme.onSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Build verification code input
  Widget _buildVerificationCodeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Enter verification code:',
          style: context.textTheme.bodyMedium,
        ),
        12.verticalSpace,
        Container(
          decoration: BoxDecoration(
            color: context.colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: context.colorScheme.primary.withValues(alpha: 0.5),
            ),
          ),
          child: TextField(
            onChanged: (value) {
              setState(() {
                _verificationCode = value;
              });
            },
            keyboardType: TextInputType.number,
            maxLength: 6,
            textAlign: TextAlign.center,
            style: context.textTheme.headlineSmallBold,
            decoration: InputDecoration(
              hintText: 'Verification code',
              border: InputBorder.none,
              counterText: '',
              contentPadding: EdgeInsets.symmetric(vertical: 16.r),
            ),
          ),
        ),
        12.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () => _sendVerificationCode(_isEmailSelected),
              child: const Text('Resend code'),
            ),
          ],
        ),
      ],
    );
  }

  // Build info section
  Widget _buildInfoSection() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: context.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: context.colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline,
                  color: context.colorScheme.primary, size: 24.r),
              8.horizontalSpace,
              Text(
                'Information',
                style: context.textTheme.headlineSmallBold.copyWith(
                  color: context.colorScheme.primary,
                ),
              ),
            ],
          ),
          12.verticalSpace,
          Text(
            'Please make sure to remember your new password. You will need to use it for future logins.',
            style: context.textTheme.bodyLargeMedium.copyWith(
              color: context.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Build action button
  Widget _buildActionButton(
      AsyncValue<ContactInfoModel?> contactInfoAsyncValue) {
    if (!_passwordSet) {
      final bool canProceed = _newPasswordData?.isValid == true &&
          _confirmPasswordData?.isValid == true &&
          _newPasswordData!.password == _confirmPasswordData!.password;

      return AuthButton(
        text: 'Continue',
        disabled: !canProceed,
        isLoading: false,
        onPressed: () {
          if (_newPasswordData!.password != _confirmPasswordData!.password) {
            LoadingUtils.showToast('Passwords do not match');
            return;
          }
          setState(() {
            _passwordSet = true;
          });
        },
        textStyle: (context, defaultStyle) => defaultStyle.copyWith(
          color: context.colorScheme.surface,
        ),
        backgroundColor: context.colorScheme.primary,
      );
    }

    final bool hasContactInfo = contactInfoAsyncValue.maybeWhen(
      data: (contactInfo) => contactInfo?.hasContactInfo ?? false,
      orElse: () => false,
    );

    final bool canChangePassword =
        hasContactInfo && !_isLoading && _verificationCode != null && _codeSent;

    return AuthButton(
      text: 'Change Password',
      disabled: !canChangePassword,
      isLoading: _isLoading,
      onPressed: () => _showConfirmDialog(),
      textStyle: (context, defaultStyle) => defaultStyle.copyWith(
        color: context.colorScheme.surface,
      ),
      backgroundColor: context.colorScheme.primary,
    );
  }

  // Send verification code
  Future<void> _sendVerificationCode(bool isEmail) async {
    try {
      LoadingUtils.showLoading();
      // Call send verification code API
      final result =
          await ref.read(accountProvider.notifier).sendVerificationCode2Self(
                isPhone: !isEmail,
              );
      LoadingUtils.dismiss();

      result.fold(
        (failure) {
          LoadingUtils.showToast(failure.message);
        },
        (right) {
          setState(() {
            _codeSent = true;
          });
          LoadingUtils.showToast('Verification code sent');
        },
      );
    } catch (e) {
      LoadingUtils.dismiss();
      LoadingUtils.showToast('Failed to send verification code: $e');
      LogUtils.e('Failed to send verification code: $e',
          tag: 'ChangePasswordScreen');
    }
  }

  // Show confirm dialog
  Future<void> _showConfirmDialog() async {
    if (_verificationCode == null || _verificationCode!.isEmpty) {
      LoadingUtils.showToast('Please enter verification code');
      return;
    }

    final result = await context.showOkCancelAlertDialog(
      title: 'Confirm Password Change',
      content: 'Are you sure you want to change your password?',
      confirmText: 'Change',
      cancelText: 'Cancel',
    );

    if (result == true) {
      _changePassword();
    }
  }

  // Change password
  Future<void> _changePassword() async {
    if (_verificationCode == null || _newPasswordData?.password == null) return;

    setState(() {
      _isLoading = true;
    });

    LoadingUtils.showLoading();

    try {
      // Call change password API
      final result = await ref.read(accountProvider.notifier).resetPassword(
            isPhone: !_isEmailSelected,
            code: _verificationCode!,
            password: _newPasswordData!.password,
          );

      result.fold(
        (failure) {
          LoadingUtils.showToast(failure.message);
        },
        (success) {
          // Success
          if (mounted) {
            context.pop();
            LoadingUtils.showToast('Password changed successfully');
          }
        },
      );
    } catch (e) {
      LogUtils.e('Failed to change password: $e', tag: 'ChangePasswordScreen');
      LoadingUtils.showToast('Failed to change password: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
