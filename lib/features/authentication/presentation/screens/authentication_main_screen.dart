import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/features/authentication/presentation/screens/login/password_login_screen.dart';
import 'package:flutter_audio_room/features/authentication/presentation/screens/signup_profile/user_name_screen.dart';
import 'package:flutter_audio_room/features/authentication/presentation/widgets/auth_button.dart';
import 'package:flutter_audio_room/flavors.dart';
import 'package:flutter_audio_room/gen/assets.gen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:url_launcher/url_launcher_string.dart';

class AuthenticationMainScreen extends StatelessWidget {
  const AuthenticationMainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        automaticallyImplyLeading: false,
      ),
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          // background image
          Positioned.fill(
            child: Container(),
          ),
          // login form
          Positioned.fill(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: AppScreenUtils.setWidth(20),
              ).copyWith(bottom: AppScreenUtils.bottomBarHeight),
              child: Column(
                children: [
                  title(context),
                  buttons(context),
                  46.verticalSpace,
                  bottom(context),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget buttons(BuildContext context) {
    return Column(
      children: [
        loginButton(context),
        30.verticalSpace,
        registerButton(context),
      ],
    );
  }

  Widget loginButton(BuildContext context) {
    return AuthButton(
      onPressed: () {
        context.push(
          RoutePageConfig(
            route: MaterialWithModalsPageRoute(
              builder: (context) => const PasswordLoginScreen(),
            ),
          ),
        );
      },
      type: AppButtonType.contained,
      backgroundColor: context.colorScheme.primary,
      width: AppScreenUtils.setWidth(226),
      text: context.l10n.login.toUpperCase(),
      textStyle: (context, defaultStyle) => defaultStyle.copyWith(
        color: context.colorScheme.onPrimary,
      ),
    );
  }

  Widget registerButton(BuildContext context) {
    return TextButton(
      onPressed: () {
        context.push(
          const WidgetPageConfig(
            page: UserNameScreen(type: 'signUp'),
          ),
        );
      },
      child: Text(
        context.l10n.signUp.toUpperCase(),
        style: context.textTheme.titleMediumSemiBold.copyWith(
          fontSize: AppScreenUtils.setFontSize(16),
          color: context.theme.colorScheme.primary,
          decoration: TextDecoration.underline,
          decorationColor: context.theme.colorScheme.primary,
          decorationThickness: 0.5,
        ),
      ),
    );
  }

  Widget title(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          const Spacer(),
          Assets.images.loginLogo.image(
            width: 314.w,
            height: 314.w,
          ),
          const Spacer(
            flex: 2,
          )
        ],
      ),
    );
  }

  Widget bottom(BuildContext context) {
    return Column(
      children: [
        Text.rich(
          TextSpan(
            children: [
              const TextSpan(text: 'By signing up you agree to the '),
              TextSpan(
                text: 'Terms of Service',
                style: context.textTheme.captionMedium.copyWith(
                  color: context.theme.colorScheme.onSurface
                      .withValues(alpha: 0.6),
                  decoration: TextDecoration.underline,
                  decorationColor:
                      context.theme.colorScheme.onSurface
                      .withValues(alpha: 0.3),
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    launchUrlString(
                      F.termsOfServiceUrl,
                    );
                  },
              ),
              const TextSpan(text: ' and '),
              TextSpan(
                text: 'Privacy Policy',
                style: context.textTheme.captionMedium.copyWith(
                  color: context.theme.colorScheme.onSurface
                      .withValues(alpha: 0.6),
                  decoration: TextDecoration.underline,
                  decorationColor:
                      context.theme.colorScheme.onSurface
                      .withValues(alpha: 0.3),
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    launchUrlString(
                      F.privacyPolicyUrl,
                    );
                  },
              ),
            ],
          ),
          style: context.textTheme.captionMedium.copyWith(
            color: context.theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
