import 'dart:collection';

import 'package:flutter_audio_room/features/audio_room/data/model/other_user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/extra_user_info_model.dart';

/// 缓存条目类，包含数据和过期时间
class CacheEntry<T> {
  final T data;
  final DateTime createdAt;
  final Duration ttl;

  CacheEntry({
    required this.data,
    required this.createdAt,
    required this.ttl,
  });

  /// 检查缓存是否过期
  bool get isExpired => DateTime.now().difference(createdAt) > ttl;

  /// 检查缓存是否仍然有效
  bool get isValid => !isExpired;
}

/// LRU 缓存实现
class LRUCache<K, V> {
  final int maxSize;
  final LinkedHashMap<K, CacheEntry<V>> _cache = LinkedHashMap();

  LRUCache({required this.maxSize});

  /// 获取缓存项
  CacheEntry<V>? get(K key) {
    final entry = _cache.remove(key);
    if (entry != null) {
      // 移动到最后（最近使用）
      _cache[key] = entry;
      return entry.isValid ? entry : null;
    }
    return null;
  }

  /// 设置缓存项
  void put(K key, V value, Duration ttl) {
    // 如果已存在，先删除
    _cache.remove(key);

    // 如果超过最大大小，删除最旧的项
    if (_cache.length >= maxSize) {
      _cache.remove(_cache.keys.first);
    }

    // 添加新项
    _cache[key] = CacheEntry(
      data: value,
      createdAt: DateTime.now(),
      ttl: ttl,
    );
  }

  /// 删除缓存项
  void remove(K key) {
    _cache.remove(key);
  }

  /// 清空缓存
  void clear() {
    _cache.clear();
  }

  /// 清理过期的缓存项
  void cleanExpired() {
    final now = DateTime.now();
    _cache.removeWhere(
        (key, entry) => now.difference(entry.createdAt) > entry.ttl);
  }

  /// 获取缓存大小
  int get size => _cache.length;

  /// 获取所有有效的缓存键
  Iterable<K> get validKeys {
    cleanExpired();
    return _cache.keys;
  }
}

/// 用户信息缓存服务
class UserInfoCacheService {
  // 默认缓存配置
  static const Duration _defaultTtl = Duration(minutes: 5); // 8分钟过期
  static const int _maxCacheSize = 100; // 最多缓存100个用户信息

  // 两个独立的缓存，分别用于不同类型的用户信息
  final LRUCache<String, ExtraUserInfoModel> _extraUserInfoCache;
  final LRUCache<String, OtherUserInfoModel> _userInfoCache;

  UserInfoCacheService({
    Duration? ttl,
    int? maxCacheSize,
  })  : _extraUserInfoCache = LRUCache(maxSize: maxCacheSize ?? _maxCacheSize),
        _userInfoCache = LRUCache(maxSize: maxCacheSize ?? _maxCacheSize);

  /// 获取用户额外信息缓存
  ExtraUserInfoModel? getExtraUserInfo(String userId) {
    final entry = _extraUserInfoCache.get(userId);
    return entry?.data;
  }

  /// 设置用户额外信息缓存
  void setExtraUserInfo(String userId, ExtraUserInfoModel userInfo,
      {Duration? ttl}) {
    _extraUserInfoCache.put(userId, userInfo, ttl ?? _defaultTtl);
  }

  /// 获取用户基本信息缓存
  OtherUserInfoModel? getUserInfo(String userId) {
    final entry = _userInfoCache.get(userId);
    return entry?.data;
  }

  /// 设置用户基本信息缓存
  void setUserInfo(String userId, OtherUserInfoModel userInfo,
      {Duration? ttl}) {
    _userInfoCache.put(userId, userInfo, ttl ?? _defaultTtl);
  }

  /// 删除指定用户的所有缓存
  void removeUserCache(String userId) {
    _extraUserInfoCache.remove(userId);
    _userInfoCache.remove(userId);
  }

  /// 清空所有缓存
  void clearAllCache() {
    _extraUserInfoCache.clear();
    _userInfoCache.clear();
  }

  /// 清理过期缓存
  void cleanExpiredCache() {
    _extraUserInfoCache.cleanExpired();
    _userInfoCache.cleanExpired();
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    cleanExpiredCache();
    return {
      'extraUserInfoCacheSize': _extraUserInfoCache.size,
      'userInfoCacheSize': _userInfoCache.size,
      'totalCacheSize': _extraUserInfoCache.size + _userInfoCache.size,
      'maxCacheSize': _maxCacheSize,
      'defaultTtlMinutes': _defaultTtl.inMinutes,
    };
  }

  /// 检查是否有指定用户的缓存
  bool hasExtraUserInfoCache(String userId) {
    return _extraUserInfoCache.get(userId) != null;
  }

  /// 检查是否有指定用户的基本信息缓存
  bool hasUserInfoCache(String userId) {
    return _userInfoCache.get(userId) != null;
  }

  /// 更新用户关注状态（用于 follow/unfollow 操作后）
  void updateUserFollowStatus(
    String userId, {
    required bool isFollowing,
    bool? isFollowed,
    int? followerCountDelta,
    int? followeeCountDelta,
    int? mutualFollowCountDelta,
  }) {
    final cachedUserInfo = getUserInfo(userId);
    if (cachedUserInfo != null) {
      final updatedUserInfo = cachedUserInfo.copyWith(
        isFollowing: isFollowing,
        isFollowed: isFollowed ?? cachedUserInfo.isFollowed,
        followerCount: followerCountDelta != null
            ? cachedUserInfo.followerCount + followerCountDelta
            : cachedUserInfo.followerCount,
        followeeCount: followeeCountDelta != null
            ? cachedUserInfo.followeeCount + followeeCountDelta
            : cachedUserInfo.followeeCount,
        mutualFollowCount: mutualFollowCountDelta != null
            ? cachedUserInfo.mutualFollowCount + mutualFollowCountDelta
            : cachedUserInfo.mutualFollowCount,
      );

      setUserInfo(userId, updatedUserInfo);
    }
  }

  /// 批量更新多个用户的关注状态（用于批量操作）
  void batchUpdateFollowStatus(Map<String, Map<String, dynamic>> updates) {
    for (final entry in updates.entries) {
      final userId = entry.key;
      final updateData = entry.value;

      updateUserFollowStatus(
        userId,
        isFollowing: updateData['isFollowing'] ?? false,
        isFollowed: updateData['isFollowed'],
        followerCountDelta: updateData['followerCountDelta'],
        followeeCountDelta: updateData['followeeCountDelta'],
        mutualFollowCountDelta: updateData['mutualFollowCountDelta'],
      );
    }
  }
}
