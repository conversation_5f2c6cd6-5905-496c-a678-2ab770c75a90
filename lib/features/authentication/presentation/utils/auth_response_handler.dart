import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/features/authentication/data/model/profile_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/home/<USER>/screens/home_screen.dart';
import 'package:flutter_audio_room/services/punishment_service/model/punishment_exception.dart';
import 'package:flutter_audio_room/services/punishment_service/model/restrict_exception.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Utility class for handling authentication API responses directly
/// without relying on global state listeners
class AuthResponseHandler {
  /// Handle authentication result and perform appropriate UI actions
  static Future<void> handleAuthResult({
    required BuildContext context,
    required WidgetRef ref,
    required Either<AppException, UserInfoModel> result,
    VoidCallback? onAccountNotExist,
  }) async {
    LoadingUtils.dismiss();

    result.fold(
      (error) => _handleAuthError(
        context: context,
        ref: ref,
        error: error,
        onAccountNotExist: onAccountNotExist,
      ),
      (user) => _handleAuthSuccess(
        context: context,
        ref: ref,
        user: user,
      ),
    );
  }

  /// Handle authentication error
  static void _handleAuthError({
    required BuildContext context,
    required WidgetRef ref,
    required AppException error,
    VoidCallback? onAccountNotExist,
  }) {
    if (error.identifier.contains('account.not.exist')) {
      onAccountNotExist?.call();
    } else if (error is PunishmentException) {
      context.showPunishmentDialog(error);
    } else if (error is RestrictException) {
      context.showRestrictDialog(error);
    } else {
      LoadingUtils.showToast(error.message.toString());
    }
  }

  /// Handle authentication success
  static Future<void> _handleAuthSuccess({
    required BuildContext context,
    required WidgetRef ref,
    required UserInfoModel user,
  }) async {
    FocusScope.of(context).unfocus();

    final profile = user.profile;
    if (profile?.deleteStatus == DeleteStatus.pending) {
      await _handleAccountDeletionPending(context, ref);
    } else {
      context.pushAndRemoveUntil(
        const WidgetPageConfig(page: HomeScreen()),
        predicate: (route) => route.isFirst,
      );
    }
  }

  /// Handle account deletion pending state
  static Future<void> _handleAccountDeletionPending(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final result = await context.showOkCancelAlertDialog(
      title: 'Account Deletion in Progress',
      content:
          'You can choose to restore your account, which will interrupt the account deletion process. Would you like to restore?',
      confirmText: 'Restore',
      cancelText: 'Exit',
    );

    if (context.mounted) {
      if (result == true) {
        LoadingUtils.showLoading();
        final restoreResult =
            await ref.read(accountProvider.notifier).restoreAccount();
        if (restoreResult.isRight() && context.mounted) {
          context.pushReplacement(
            const WidgetPageConfig(page: HomeScreen()),
          );
        }
        LoadingUtils.dismiss();
      } else {
        await ref.read(accountProvider.notifier).logout();
      }
    }
  }
}
