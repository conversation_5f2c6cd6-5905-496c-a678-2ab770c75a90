import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_message_model.freezed.dart';
part 'chat_message_model.g.dart';

/// 文字聊天消息模型
/// 专门用于Stream Channel的文字聊天功能
@freezed
class ChatMessageModel with _$ChatMessageModel {
  const factory ChatMessageModel({
    required String id,
    required String content,
    required int senderId, // agora uid
    required String senderName,
    String? senderAvatar,
    required int timestamp,
    required ChatMessageType type,
    
    // 可选字段
    String? roomId,
    Map<String, dynamic>? extra,
  }) = _ChatMessageModel;

  factory ChatMessageModel.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageModelFromJson(json);
}

/// 聊天消息类型
enum ChatMessageType {
  /// 普通文字消息
  text,
  /// 系统消息（用户进入、离开等）
  system,
  /// 通知消息
  notification,
}

/// 聊天消息扩展方法
extension ChatMessageModelExtension on ChatMessageModel {
  /// 是否为系统消息
  bool get isSystemMessage => type == ChatMessageType.system;
  
  /// 是否为普通文字消息
  bool get isTextMessage => type == ChatMessageType.text;
  
  /// 是否为通知消息
  bool get isNotificationMessage => type == ChatMessageType.notification;
  
  /// 创建文字消息
  static ChatMessageModel createTextMessage({
    required String content,
    required int senderId,
    required String senderName,
    String? senderAvatar,
    String? roomId,
    Map<String, dynamic>? extra,
  }) {
    return ChatMessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      senderId: senderId,
      senderName: senderName,
      senderAvatar: senderAvatar,
      timestamp: DateTime.now().millisecondsSinceEpoch,
      type: ChatMessageType.text,
      roomId: roomId,
      extra: extra,
    );
  }
  
  /// 创建系统消息
  static ChatMessageModel createSystemMessage({
    required String content,
    String? roomId,
    Map<String, dynamic>? extra,
  }) {
    return ChatMessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      senderId: 0, // 系统消息使用0作为发送者ID
      senderName: 'System',
      timestamp: DateTime.now().millisecondsSinceEpoch,
      type: ChatMessageType.system,
      roomId: roomId,
      extra: extra,
    );
  }
}
