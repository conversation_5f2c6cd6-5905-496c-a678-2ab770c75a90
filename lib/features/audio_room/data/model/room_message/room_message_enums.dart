
  
enum RoomMessageFollowActionSource {
  followBack,
  giftSender,
  giftReceiver;
}

enum RoomMessageEvent {
  newMessage,
  systemMessage, // announcement, appreguale
  userStatus,
  followCreator,
  giftMessage,
  actionMessage,
  followBack,
  textChat; // 新增文字聊天事件
}

enum RoomMessageEventSubtype {
  // userStatus
  joinRoom,
  dropOnMic,
  quitManager,

  // actionMessage
  micRequest,
  agreeMicRequest,
  rejectMicRequest,
  inviteOnMic,
  agreeInviteOnMic,
  rejectInviteOnMic,
  inviteManager,
  rejectInviteManager,
  agreeInviteManager,
  removeManager,
  muteMic,
  unmuteMic,
  changePositionRequest,
  levelup,
  userMuteMic,
  userUnmuteMic,
  videoPlay,
  videoPause,
  videoEnd,
  videoSeek,
  kickOut,
}

enum RoomMessageExtraType {
  followBack;
}

enum RoomGiftType {
  mysteryBox,
  free,
  pay;
}
