import 'package:agora_rtm/agora_rtm.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_user.dart';

import '../../../../shared/domain/types/common_types.dart';

/// Interface for RTM (Real-Time Messaging) service
abstract class IRtmService {
  /// Core properties
  RtmClient? get client;
  String? get channelName;
  String? get token;
  RtmStorage? get storage;
  RtmPresence? get presence;

  /// Core setters
  void setClient(RtmClient? client);
  void setChannelName(String? channelName);
  void setToken(String? token);
  void setStorage(RtmStorage? storage);
  void setPresence(RtmPresence? presence);
  void setupEventListeners(RtmClient client);
  void removeEventListeners(RtmClient? client);

  /// Event handlers
  set onMessageEvent(void Function(MessageEvent event)? value);
  set onPresenceEvent(void Function(PresenceEvent event)? value);
  set onStorageEvent(void Function(StorageEvent event)? value);
  set onTopicEvent(void Function(TopicEvent event)? value);
  set onTokenPrivilegeWillExpire(void Function(TokenEvent event)? value);
  set onLinkStateEvent(void Function(LinkStateEvent event)? value);
  
  /// Metadata conversion
  RoomUser? convertMetadataToUser(List<StateItem> metadata);
  Map<String, String> convertUserToMetadata(RoomUser user);

  /// RTM operations
  Future<ResultWithData<T>> executeRtmOperation<T>({
    required String operation,
    required Future<(RtmStatus, T?)> Function() action,
    T? defaultValue,
    bool hasJoinChannel = true,
  });

  /// Initialize the RTM service with user ID and app ID
  Future<VoidResult> initialize({
    required String userId,
    required String appId,
    RtmEncryptionConfig? encryptionConfig,
  });

  /// Login to RTM service with token
  Future<VoidResult> login(String token, String channelId);

  /// fetch channel members
  Future<ResultWithData<List<RoomUser>>> fetchChannelMembers();

  /// fetch channel info
  Future<ResultWithData<List<MetadataItem>>> fetchChannelInfo();

  /// set user metadata
  Future<VoidResult> setUserMetadata(RoomUser user);

  /// get user metadata
  Future<ResultWithData<RoomUser?>> getUserMetadata(String uid);

  /// update channel info
  Future<VoidResult> setChannelMultiMetadata(List<MetadataItem> info);

  /// update channel metadata
  Future<VoidResult> updateChannelMetadata(List<MetadataItem> info);

  /// remove channel metadata
  /// [keys] can be null, if null, all metadata will be removed
  Future<VoidResult> removeChannelMetadata(List<String>? keys);

  /// Send message to current channel
  Future<VoidResult> sendChannelMessage(RoomMessageModel message);

  /// Send message to user
  Future<VoidResult> sendMessageToUser(String uid, RoomMessageModel message);

  // ========== Stream Channel 相关方法 ==========

  /// 创建并加入 Stream Channel
  Future<VoidResult> createAndJoinStreamChannel();

  /// 发送文字聊天消息
  Future<VoidResult> sendChatMessage(ChatMessageModel message);

  /// 离开 Stream Channel
  Future<VoidResult> leaveStreamChannel();

  /// 订阅文字聊天 Topic
  Future<VoidResult> subscribeChatTopic({List<String>? users});

  /// 取消订阅文字聊天 Topic
  Future<VoidResult> unsubscribeChatTopic({List<String>? users});

  /// Leave channel
  Future<VoidResult> leaveChannel();

  /// Logout from RTM service
  Future<VoidResult> logout();

  /// Renew token
  Future<VoidResult> renewToken();

  /// Dispose RTM service
  Future<VoidResult> dispose();
}
