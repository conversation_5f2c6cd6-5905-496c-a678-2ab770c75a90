import 'dart:async';

import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_rtm_service.dart';
import 'package:flutter_audio_room/features/audio_room/domain/services/chat_message_stream.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/handlers/message/chat_message_handler.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';

/// 聊天服务
/// 整合RTM Stream Channel和聊天消息处理的完整服务
class ChatService {
  final IRtmService _rtmService;
  final ChatMessageStream _messageStream;
  final ChatMessageHandler _messageHandler;
  
  StreamSubscription? _topicEventSubscription;
  StreamSubscription? _messageEventSubscription;

  ChatService(this._rtmService)
      : _messageStream = ChatMessageStream(),
        _messageHandler = ChatMessageHandler(ChatMessageStream());

  /// 获取聊天消息流
  Stream<ChatMessageModel> get messageStream => _messageStream.stream;

  /// 获取文字消息流
  Stream<ChatMessageModel> get textMessageStream => _messageStream.textMessages;

  /// 获取系统消息流
  Stream<ChatMessageModel> get systemMessageStream => _messageStream.systemMessages;

  /// 初始化聊天服务
  Future<VoidResult> initialize() async {
    // 创建并加入Stream Channel
    final result = await _rtmService.createAndJoinStreamChannel();
    if (result.isLeft()) return result;

    // 订阅文字聊天Topic
    final subscribeResult = await _rtmService.subscribeChatTopic();
    if (subscribeResult.isLeft()) return subscribeResult;

    // 设置事件监听
    _setupEventListeners();

    return const Right(null);
  }

  /// 发送文字消息
  Future<VoidResult> sendTextMessage({
    required String content,
    required int senderId,
    required String senderName,
    String? senderAvatar,
    String? roomId,
  }) async {
    final message = ChatMessageModel.createTextMessage(
      content: content,
      senderId: senderId,
      senderName: senderName,
      senderAvatar: senderAvatar,
      roomId: roomId,
    );

    return await _rtmService.sendChatMessage(message);
  }

  /// 添加系统消息
  void addSystemMessage(String content, {String? roomId}) {
    _messageHandler.addSystemMessage(content, roomId: roomId);
  }

  /// 处理房间消息（将系统消息转换为聊天消息）
  void handleRoomMessage(RoomMessageModel roomMessage) {
    _messageHandler.handleRoomMessage(roomMessage);
  }

  /// 设置事件监听
  void _setupEventListeners() {
    // 监听Topic事件（Stream Channel消息）
    _rtmService.onTopicEvent = (event) {
      _messageHandler.handleTopicMessage(event);
    };

    // 监听Message事件（用于系统消息转换）
    _rtmService.onMessageEvent = (event) {
      // 这里可以处理来自Message Channel的消息
      // 并将相关的系统消息转换为聊天消息
    };
  }

  /// 离开聊天
  Future<VoidResult> leave() async {
    // 取消事件监听
    await _topicEventSubscription?.cancel();
    await _messageEventSubscription?.cancel();

    // 离开Stream Channel
    return await _rtmService.leaveStreamChannel();
  }

  /// 清理资源
  void dispose() {
    _topicEventSubscription?.cancel();
    _messageEventSubscription?.cancel();
    _messageStream.dispose();
  }

  /// 获取消息流状态
  bool get isStreamClosed => _messageStream.isClosed;

  /// 手动添加消息到流（用于测试或特殊情况）
  void addMessage(ChatMessageModel message) {
    _messageStream.add(message);
  }

  /// 创建并发送系统通知
  void sendSystemNotification(String content, {String? roomId}) {
    final message = ChatMessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      senderId: 0,
      senderName: 'System',
      timestamp: DateTime.now().millisecondsSinceEpoch,
      type: ChatMessageType.notification,
      roomId: roomId,
    );
    _messageStream.add(message);
  }

  /// 批量添加历史消息
  void addHistoryMessages(List<ChatMessageModel> messages) {
    for (final message in messages) {
      _messageStream.add(message);
    }
  }
}

/// 聊天服务工厂
class ChatServiceFactory {
  /// 创建聊天服务实例
  static ChatService create(IRtmService rtmService) {
    return ChatService(rtmService);
  }

  /// 创建并初始化聊天服务
  static Future<Either<Exception, ChatService>> createAndInitialize(
    IRtmService rtmService,
  ) async {
    final chatService = ChatService(rtmService);
    final result = await chatService.initialize();
    
    return result.fold(
      (error) => Left(error),
      (_) => Right(chatService),
    );
  }
}
