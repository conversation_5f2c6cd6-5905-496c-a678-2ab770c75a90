import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_mini_player_service.dart';

import '../../widgets/mini_player_widget.dart';

class MiniPlayerService implements IMiniPlayerService {
  MiniPlayerService() {
    _lastPosition = Offset(
      AppScreenUtils.setWidth(20),
      AppScreenUtils.setHeight(100),
    );
    _animationController = AnimationController(
      duration: _animationDuration,
      vsync: _tickerProvider,
    );
  }

  static const Duration _animationDuration = Duration(milliseconds: 400);
  static const Curve _animationCurve = Curves.easeOutQuart;

  OverlayEntry? _overlayEntry;
  late Offset _lastPosition;
  late final AnimationController _animationController;
  Animation<Offset>? _positionAnimation;
  Route? _currentPageRouter;

  final double _miniPlayerWidth = AppScreenUtils.setWidth(50);
  final double _miniPlayerHeight = AppScreenUtils.setHeight(50);

  @override
  Route? get currentPageRouter => _currentPageRouter;

  @override
  Offset get miniPlayerPosition => _lastPosition;
  double get miniPlayerSize => _miniPlayerWidth;

  // Add ticker provider mixin
  final _SingleTickerProviderImpl _tickerProvider = _SingleTickerProviderImpl();

  @override
  void showMiniPlayer<T>(BuildContext context, Route<T> routerConfig) {
    if (_overlayEntry != null) return;

    _currentPageRouter = routerConfig;

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          Positioned(
            left: _lastPosition.dx,
            top: _lastPosition.dy,
            child: GestureDetector(
              onPanUpdate: (details) {
                _lastPosition += details.delta;
                _overlayEntry?.markNeedsBuild();
              },
              onPanEnd: (details) {
                _checkAndAnimateBoundaries(context);
              },
              child: const MiniPlayerWidget(),
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  @override
  void removeMiniPlayer() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _animationController.stop();
  }

  void _checkAndAnimateBoundaries(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final safeAreaTop = MediaQuery.of(context).padding.top;
    final safeAreaBottom = MediaQuery.of(context).padding.bottom;

    double targetX = _lastPosition.dx;
    double targetY = _lastPosition.dy;

    // Horizontal direction: Automatically adsorbs to the nearest edge
    final distanceToLeft = targetX;
    final distanceToRight = screenSize.width - (targetX + _miniPlayerWidth);

    // Determine which side is closer
    if (distanceToLeft < distanceToRight) {
      targetX = AppScreenUtils.setWidth(10); // Adsorb to the left
    } else {
      targetX = screenSize.width -
          _miniPlayerWidth -
          AppScreenUtils.setWidth(10); // Adsorb to the right
    }

    // Vertical direction: Keep in the safe area and do not adsorb
    final topBoundary = safeAreaTop;
    final bottomBoundary =
        screenSize.height - safeAreaBottom - _miniPlayerHeight;

    if (targetY < topBoundary) {
      targetY = topBoundary;
    } else if (targetY > bottomBoundary) {
      targetY = bottomBoundary;
    }

    // If the position changes, perform an animation
    if (targetX != _lastPosition.dx || targetY != _lastPosition.dy) {
      _animatePosition(Offset(targetX, targetY));
    }
  }

  void _animatePosition(Offset target) {
    _animationController.reset();

    _positionAnimation = Tween<Offset>(
      begin: _lastPosition,
      end: target,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: _animationCurve,
    ));

    void listener() {
      _lastPosition = _positionAnimation!.value;
      _overlayEntry?.markNeedsBuild();
    }

    _animationController.addListener(listener);
    _animationController.forward().then((_) {
      _animationController.removeListener(listener);
    });
  }

  @override
  void dispose() {
    removeMiniPlayer();
    _animationController.dispose();
    _tickerProvider.dispose();
  }
}

// Helper class for ticker provider with proper disposal
class _SingleTickerProviderImpl implements TickerProvider {
  Ticker? _ticker;

  @override
  Ticker createTicker(TickerCallback onTick) {
    _ticker?.dispose();
    _ticker = Ticker(onTick);
    return _ticker!;
  }

  void dispose() {
    _ticker?.dispose();
    _ticker = null;
  }
}
