import 'package:agora_rtm/agora_rtm.dart' as rtm;
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

import '../../../domain/interfaces/i_rtm_service.dart';
import 'mixins/rtm_auth_mixin.dart';
import 'mixins/rtm_channel_mixin.dart';
import 'mixins/rtm_metadata_mixin.dart';
import 'mixins/rtm_stream_channel_mixin.dart';
import 'mixins/rtm_user_mixin.dart';
import 'rtm_event_handler_mixin.dart';

/// Implementation of RTM service using Agora
class AgoraRtmService extends IRtmService
    with
        RtmEventHandlerMixin,
        RtmMetadataMixin,
        RtmChannelMixin,
        RtmStreamChannelMixin,
        RtmUserMixin,
        RtmAuthMixin {
  rtm.RtmClient? _client;
  String? _channelName;
  String? _token;
  rtm.RtmStorage? _storage;
  rtm.RtmPresence? _presence;

  @override
  rtm.RtmClient? get client => _client;
  @override
  String? get channelName => _channelName;
  @override
  String? get token => _token;
  @override
  rtm.RtmStorage? get storage => _storage;
  @override
  rtm.RtmPresence? get presence => _presence;

  @override
  void setClient(rtm.RtmClient? client) => _client = client;
  @override
  void setChannelName(String? channelName) => _channelName = channelName;
  @override
  void setToken(String? token) => _token = token;
  @override
  void setStorage(rtm.RtmStorage? storage) => _storage = storage;
  @override
  void setPresence(rtm.RtmPresence? presence) => _presence = presence;

  @override
  Future<ResultWithData<T>> executeRtmOperation<T>({
    required String operation,
    required Future<(rtm.RtmStatus, T?)> Function() action,
    T? defaultValue,
    bool hasJoinChannel = true,
  }) async {
    if (hasJoinChannel && _client == null) {
      return _handleClientNotInitializedWithType<T>(operation);
    }

    if (hasJoinChannel && _channelName == null) {
      return _handleChannelNotJoinedWithType<T>(operation);
    }

    try {
      final (status, result) = await action();
      if (status.error) {
        return Left(AppException(
          message: '${status.errorCode}, ${status.reason}',
          statusCode: 400,
          identifier: 'RtmService.$operation',
        ));
      }
      return Right(result ?? defaultValue as T);
    } catch (e) {
      return Left(AppException(
        message: e.toString(),
        statusCode: 400,
        identifier: 'RtmService.$operation',
      ));
    }
  }

  ResultWithData<T> _handleClientNotInitializedWithType<T>(
      String operation) {
    return Left(AppException(
      message: 'RTM client not initialized',
      statusCode: 400,
      identifier: operation,
    ));
  }

  ResultWithData<T> _handleChannelNotJoinedWithType<T>(String operation) {
    return Left(AppException(
      message: 'Channel not joined',
      statusCode: 400,
      identifier: operation,
    ));
  }

  @override
  Future<VoidResult> dispose() async {
    try {
      final leaveResult = await leaveChannel();
      if (leaveResult.isLeft()) {
        return leaveResult;
      }

      final logoutResult = await logout();
      if (logoutResult.isLeft()) {
        return logoutResult;
      }

      return const Right(null);
    } catch (e) {
      return Left(AppException(
        message: e.toString(),
        statusCode: 400,
        identifier: 'RtmService.dispose',
      ));
    }
  }
}
