import 'dart:async';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/list_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_scaffold.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_enums.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/leave_room_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/tap_user_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/screens/report_user_screen.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_screen/audio_room_appbar.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_screen/audio_room_controllers.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_screen/audio_room_seat_grid.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_screen/listener_list.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_screen/room_messages_list.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_wrapper/audio_room_wrapper.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/modal/edit_room_info_modal.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/modal/grid_actions_modal.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/modal/host_guide_modal.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/modal/leave_room_modal.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/modal/share_room_modal.dart';
import 'package:flutter_audio_room/features/authentication/data/model/avatar_frame_model.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_config.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/chat_provider.dart';
import 'package:flutter_audio_room/gen/assets.gen.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/gift_type.dart';
import 'package:flutter_audio_room/services/gift_service/presentation/provider/gift_provider.dart';
import 'package:flutter_audio_room/services/screen_protector_service/i_screen_protector_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AudioRoomScreen extends ConsumerStatefulWidget {
  const AudioRoomScreen({super.key, this.fromMiniPlayer = false});

  final bool fromMiniPlayer;

  static const String routeName = 'audio_room_screen';

  static Route<AudioRoomScreen> route({bool fromMiniPlayer = false}) {
    return PageRouteBuilder(
      fullscreenDialog: true,
      barrierColor: Colors.transparent,
      opaque: false,
      settings: const RouteSettings(name: routeName),
      pageBuilder: (context, animation, secondaryAnimation) {
        // 如果是从小球展开，不使用默认的滑动动画，让自定义动画接管
        if (fromMiniPlayer) {
          return AudioRoomScreen(fromMiniPlayer: fromMiniPlayer);
        }

        // 正常进入时使用滑动动画
        final curvedAnimation = CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
          reverseCurve: Curves.easeInCubic,
        );

        return AnimatedBuilder(
          animation: curvedAnimation,
          builder: (context, child) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 1),
                end: Offset.zero,
              ).animate(curvedAnimation),
              child: child!,
            );
          },
          child: AudioRoomScreen(fromMiniPlayer: fromMiniPlayer),
        );
      },
    );
  }

  @override
  ConsumerState<AudioRoomScreen> createState() => _AudioRoomScreenState();
}

class _AudioRoomScreenState extends ConsumerState<AudioRoomScreen>
    with LeaveRoomMixin, TapUserMixin {
  StreamSubscription? _screenshotSubscription;
  StreamSubscription? _screenRecordSubscription;
  StreamSubscription? _messageStreamSubscription;

  final _screenProtectorService = getIt<IScreenProtectorService>();
  bool _isScreenRecord = false;

  @override
  void initState() {
    super.initState();
    _screenProtector();
    _setConnectionStateChanged();
    _setupListener();
  }

  @override
  void deactivate() {
    ref.read(audioRoomProvider.notifier).removeConnectionStateChangedCallback(
          'AudioRoomScreen',
        );
    super.deactivate();
  }

  @override
  void dispose() {
    _screenshotSubscription?.cancel();
    _screenRecordSubscription?.cancel();
    _messageStreamSubscription?.cancel();
    super.dispose();
  }

  void _setupListener() {
    _messageStreamSubscription = ref
        .read(audioRoomProvider.notifier)
        .messageStreamData
        .listen((message) {
      final messageType = message.eventSubtype;

      if (message.event == RoomMessageEvent.giftMessage) {
        _handleGiftMessage(message);
        return;
      }

      switch (messageType) {
        case RoomMessageEventSubtype.micRequest:
          _handleMicRequest(message);
          break;
        case RoomMessageEventSubtype.inviteOnMic:
          _handleInviteOnMic(message);
          break;
        case RoomMessageEventSubtype.inviteManager:
          _handleInviteManager(message);
          break;
        case RoomMessageEventSubtype.agreeInviteManager:
          break;
        default:
          break;
      }
    });
  }

  void _setConnectionStateChanged() {
    ref.read(audioRoomProvider.notifier).setConnectionStateChangedCallback(
      'AudioRoomScreen',
      (state, reason) async {
        if ((state == ConnectionStateType.connectionStateFailed ||
                state == ConnectionStateType.connectionStateDisconnected) &&
            reason ==
                ConnectionChangedReasonType.connectionChangedBannedByServer) {
          context.popUntilFirst();
        } else if (state == ConnectionStateType.connectionStateFailed) {}
      },
    );
  }

  void _screenProtector() {
    _screenshotSubscription = _screenProtectorService.onScreenshot.listen((_) {
      LogUtils.d('Screenshot', tag: 'AudioRoomScreen._screenProtector');
    });
    _screenRecordSubscription =
        _screenProtectorService.onScreenRecord.listen((isRecording) {
      setState(() {
        _isScreenRecord = isRecording;
      });
    });
  }

  void _handleMicRequest(RoomMessageModel message) async {
    final dialogTag = 'mic_request_dialog_${message.senderId}';

    final result = await context.showOkCancelAlertDialog(
      title: context.l10n.micRequest,
      content: '${message.senderId} wants to speak',
      dialogTag: dialogTag,
    );
    final uid = message.senderId;
    final res = await ref.read(audioRoomProvider.notifier).handleMicRequest(
          targetUid: uid,
          targetSeat: message.position?.targetPosition,
          accept: result,
        );
    res.fold((l) => LoadingUtils.showError(l.message), (r) => null);
  }

  void _handleInviteOnMic(RoomMessageModel message) async {
    if (message.targetId != ref.read(audioRoomProvider).currentUid) return;
    final dialogTag = 'invite_on_mic_dialog_${message.senderId}';

    final result = await context.showOkCancelAlertDialog(
      title: context.l10n.micInvite,
      content: '${message.senderId} invited you to speak',
      dialogTag: dialogTag,
    );
    final uid = message.senderId;
    final res = await ref.read(audioRoomProvider.notifier).handleMicInvite(
          uid,
          result == true,
        );
    res.fold((l) => LoadingUtils.showError(l.message), (r) => null);
  }

  void _handleInviteManager(RoomMessageModel message) async {
    final uid = message.targetId;

    if (uid != ref.read(audioRoomProvider).currentUid) return;
    final dialogTag = 'invite_manager_dialog_${message.senderId}';

    final result = await context.showOkCancelAlertDialog(
      title: context.l10n.managerInvite,
      content: '${message.senderId} invited you to be a manager',
      dialogTag: dialogTag,
    );
    final res = await ref
        .read(audioRoomProvider.notifier)
        .handleManagerInvite(uid, result == true);
    res.fold((l) => LoadingUtils.showError(l.message), (r) => null);
  }  

  Future<void> _onEditRoomInfo() async {
    await showAdaptiveDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => const EditRoomInfoModal(),
    );
  }

  Future<void> _showShareRoomModal() async {
    await context.showCupertinoModalBottomSheet(
      builder: (context) => ShareRoomModal(
        onSelectUser: (user) {},
        onShare: (user) async {
          context.pop();

          final currentRoom = ref.read(audioRoomProvider).currentRoom;
          final currentUser = ref.read(audioRoomProvider).currentUser;
          if (currentRoom == null || currentUser == null) {
            LoadingUtils.showToast('Share room failed, please try again later');
            return;
          }

          final result = await ref
              .read(chatProvider(user.profile?.id ?? '').notifier)
              .sendMessage(
                InviteRoomMessageConfig(
                  userId: currentUser.userId ?? '',
                  roomInfo: currentRoom,
                ),
              );
          result.fold(
            (e) {
              LoadingUtils.showToast(
                  'Share room failed, please try again later');
            },
            (r) {
              LoadingUtils.showToast('Share room successfully');
            },
          );
        },
      ),
    );
  }

  Future<void> _showHostGuideModal() async {
    await showAdaptiveDialog(
        context: context,
        builder: (context) {
          return HostGuideModal(
            onPressed: () {
              context.pop();
            },
          );
        });
  }

  Future<void> _showRoomActionsModal(Future<void> Function() onClose) async {
    final isOwner = ref.read(audioRoomProvider).isCreator;
    final actions = [
      ActionItem(
        icon: Assets.images.actionsReportIssue.image(),
        text: context.l10n.reportIssue,
        onPressed: () async {
          context.pop();
          toReportRoomPage(
            RoomReportSource(
              roomId: ref.read(audioRoomProvider).currentRoom?.id ?? '',
            ),
          );
        },
      ),
      ActionItem(
        icon: Assets.images.actionsShareRoom.image(),
        text: context.l10n.shareRoom,
        onPressed: () async {
          context.pop();
          _showShareRoomModal();
        },
      ),
      ActionItem(
        icon: Assets.images.actionsReportUser.image(),
        text: context.l10n.reportUser,
        onPressed: () async {
          context.pop();
          await Future.delayed(const Duration(milliseconds: 200));
          handleReportAction();
        },
      ),
    ];

    if (!isOwner) {
      actions.add(
        ActionItem(
          icon: Assets.images.actionsCloseRoom.image(),
          text: context.l10n.leaveRoom,
          onPressed: () async {
            context.pop();
            final result = await showAdaptiveDialog(
              context: context,
              builder: (ctx) {
                return LeaveRoomModal(
                  isCreator: isOwner,
                );
              },
            );
            if (result == true) {
              await leaveRoom();
              await onClose();
            }
          },
        ),
      );
    }

    if (isOwner) {
      actions.insertAll(0, [
        ActionItem(
          icon: Assets.images.actionsRoomInfo.image(),
          text: context.l10n.roomInfo,
          onPressed: () async {
          context.pop();
            _onEditRoomInfo();
        },
        ),
        ActionItem(
          icon: Assets.images.actionsHostGuide.image(),
          text: context.l10n.hostGuide,
          onPressed: () async {
            context.pop();
            _showHostGuideModal();
          },
        ),
      ]);
      actions.add(ActionItem(
        icon: Assets.images.actionsCloseRoom.image(),
        text: context.l10n.endRoom,
        onPressed: () async {
          context.pop();
          final result = await showAdaptiveDialog(
            context: context,
            builder: (ctx) {
              return LeaveRoomModal(
                isCreator: isOwner,
              );
            },
          );

          if (result == true) {
            final res = await leaveRoom();
            if (res.isRight()) {
              await onClose();
            }
          }
        },
      ));
    }
    await showAdaptiveDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => GridActionMenu(
        actions: actions,
      ),
    );
  }

  Future<void> _handleGiftMessage(RoomMessageModel message) async {
    final giftType = message.gift?.giftType;
    if (giftType == null) return;

    if (!mounted) return;
    const dialogTag = 'gift_message_dialog';
    await context.showFullscreenGiftEffect(
      svgaUrl: message.gift?.svgaUrl,
      imageUrl: message.gift?.imageUrl,
      dialogTag: dialogTag,
      giftType: giftType,
    );

    switch (giftType) {
      case GiftType.frame:
        _handleFrameGift(message);
        break;
      case GiftType.gift:
        _handleGift(message);
        break;
    }
  }

  Future<void> _handleFrameGift(RoomMessageModel message) async {
    final uid = message.giftReceives?.firstWhereOrNull(
        (uid) => uid == ref.read(audioRoomProvider).currentUid);

    if (uid == null) return;

    final gift = message.gift;
    if (gift == null) return;

    final giftBagId = gift.sendGiftResults?.firstOrNull?.giftBagId;
    if (giftBagId == null) return;

    final res = await context.showOkCancelAlertDialog(
      title: 'Receive Frame',
      content: '${message.senderId} sent you a frame, do you want to use it?',
    );

    if (res == false) return;

    final result = await ref
        .read(giftStateNotifierProvider.notifier)
        .useAvatarFrame(giftBagId: giftBagId);
    result.fold((l) => LoadingUtils.showError(l.message), (r) async {
      final avatarFrame = AvatarFrameModel.fromJson(gift.toJson()).copyWith(
        giftStatus: 'active',
        activateExpireTime:
            DateTime.now().add(Duration(days: gift.giftExpiryDays ?? 1)),
      );
      await ref.read(audioRoomProvider.notifier).updateUserFrame(
            uid: uid,
            avatarFrame: avatarFrame,
          );

      await ref.read(accountProvider.notifier).updateUserFrame(
            avatarFrame: avatarFrame,
          );
    });
  }

  Future<void> _handleGift(RoomMessageModel message) async {}

  @override
  Widget build(BuildContext context) {

    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: AudioRoomWrapper(
        fromMiniPlayer: widget.fromMiniPlayer,
        route: AudioRoomScreen.route(fromMiniPlayer: widget.fromMiniPlayer),
        builder: (context, onClose, onMinimize) {
          return AppScaffold(
            backgroundColor: Colors.transparent,
            resizeToAvoidBottomInset: false,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AudioRoomAppBar(
                  onActions: () => _showRoomActionsModal(onClose),
                  onMinimize: () =>
                      onMinimize(AudioRoomScreen.route(fromMiniPlayer: true)),
                ),
                Expanded(
                  child: Opacity(
                    opacity: _isScreenRecord ? 0 : 1,
                    child: Column(
                      children: [
                        40.verticalSpace,
                        const SeatGrid(),
                        20.verticalSpace,
                        Expanded(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Column(
                                  children: [
                                    _appAnnouncement(),
                                    const Expanded(
                                      child: RoomMessagesList(),
                                    ),
                                  ],
                                ),
                              ),
                              14.horizontalSpace,
                              Column(
                                children: [
                                  const ListenerList(),
                                  9.verticalSpace,
                                  GestureDetector(
                                    onTap: () {
                                      _showShareRoomModal();
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: context.theme.colorScheme.surface
                                            .withValues(alpha: 0.3),
                                        borderRadius:
                                            BorderRadius.circular(999),
                                      ),
                                      padding: EdgeInsets.all(
                                        AppScreenUtils.setWidth(5),
                                      ),
                                      child: Assets.svgs.shareRoom.svg(
                                        width: 24.w,
                                        height: 24.w,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const AudioRoomControls(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _appAnnouncement() {
    final String announcement = ref.watch(audioRoomProvider.select((state) {
      return state.currentRoom?.announcement ?? '';
    }));
    final ThemeData theme = Theme.of(context);
    final Color announcementBackgroundColor =
        context.theme.colorScheme.surface.withValues(alpha: 0.3);
    const Color announcementTextColor = Color(0xFFE8BB08);
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: announcementBackgroundColor,
        borderRadius: BorderRadius.circular(
          AppScreenUtils.setRadius(8),
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: AppScreenUtils.setWidth(10),
        vertical: AppScreenUtils.setHeight(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '📢 ${context.l10n.appAnnouncement}',
            style: theme.textTheme.bodyMedium
                ?.copyWith(color: announcementTextColor),
          ),
          Text(
            announcement,
            style: theme.textTheme.bodyMedium
                ?.copyWith(color: announcementTextColor),
          ),
        ],
      ),
    );
  }
}
