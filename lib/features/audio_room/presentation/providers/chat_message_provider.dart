import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'chat_message_provider.g.dart';

/// 聊天消息状态管理
@Riverpod(keepAlive: true)
class ChatMessage extends _$ChatMessage {
  @override
  List<ChatMessageModel> build() {
    return [];
  }

  /// 添加消息
  void addMessage(ChatMessageModel message) {
    state = [...state, message];
  }

  /// 添加多条消息
  void addMessages(List<ChatMessageModel> messages) {
    state = [...state, ...messages];
  }

  /// 移除消息
  void removeMessage(String messageId) {
    state = state.where((m) => m.id != messageId).toList();
  }

  /// 清空所有消息
  void clear() {
    state = [];
  }

  /// 获取文字消息
  List<ChatMessageModel> get textMessages =>
      state.where((m) => m.type == ChatMessageType.text).toList();

  /// 获取系统消息
  List<ChatMessageModel> get systemMessages =>
      state.where((m) => m.type == ChatMessageType.system).toList();

  /// 获取通知消息
  List<ChatMessageModel> get notificationMessages =>
      state.where((m) => m.type == ChatMessageType.notification).toList();

  /// 获取最新的N条消息
  List<ChatMessageModel> getLatestMessages(int count) {
    if (state.length <= count) return state;
    return state.sublist(state.length - count);
  }

  /// 按时间排序的消息列表
  List<ChatMessageModel> get sortedMessages {
    final sorted = [...state];
    sorted.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return sorted;
  }
}

/// 聊天消息流Provider
@riverpod
Stream<ChatMessageModel> chatMessageStream(ChatMessageStreamRef ref) async* {
  // 这里会在实际使用时连接到ChatMessageStream
  // 暂时返回空流
  yield* const Stream.empty();
}
