import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/core/widgets/dialog_wrapper.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Action item data model
class ActionItem {
  final String text;
  final Widget? icon;
  final Future<void> Function() onPressed;

  const ActionItem({
    required this.text,
    this.icon,
    required this.onPressed,
  });
}

/// A flexible grid action menu that adapts to the number of items
class GridActionMenu extends StatelessWidget {
  const GridActionMenu({
    super.key,
    required this.actions,
    this.maxCrossAxisCount = 4,
    this.itemTextColor = Colors.white,
    this.itemTextStyle,
    this.itemSize,
    this.itemSpacing,
    this.borderRadius,
  });

  /// List of actions to display
  final List<ActionItem> actions;

  /// Maximum number of items per row
  final int maxCrossAxisCount;

  /// Text color of action items
  final Color itemTextColor;

  /// Custom text style for action items
  final TextStyle? itemTextStyle;

  /// Size of action items (width and height will be equal)
  final double? itemSize;

  /// Spacing between items
  final double? itemSpacing;

  /// Border radius of the menu container
  final BorderRadius? borderRadius;

  /// Calculate optimal cross axis count based on number of actions
  int _calculateCrossAxisCount() {
    if (actions.length >= maxCrossAxisCount) {
      return maxCrossAxisCount;
    }
    return actions.length;
  }

  /// Calculate child aspect ratio based on available width and actions count
  double _calculateChildAspectRatio(double availableWidth, double itemHeight) {
    final crossAxisCount = _calculateCrossAxisCount();
    // 计算每个项目的宽度（考虑间距）
    final totalSpacing = (crossAxisCount - 1) * (itemSpacing ?? 0);
    final itemWidth = (availableWidth - totalSpacing) / crossAxisCount;
    // 返回宽高比
    return itemWidth / itemHeight;
  }

  @override
  Widget build(BuildContext context) {
    final crossAxisCount = _calculateCrossAxisCount();
    final defaultItemSize = AppScreenUtils.setWidth(42);
    final defaultSpacing = 15.h;

    // 计算项目实际高度（图标高度 + 间距 + 文本高度）
    final iconHeight = itemSize ?? defaultItemSize;
    const textHeight = 12.0; // 估计文本高度
    final itemHeight =
        iconHeight + (itemSpacing ?? defaultSpacing) + textHeight;

    return LayoutBuilder(builder: (context, constraints) {
      final availableWidth =
          constraints.maxWidth - 20.h - (itemSpacing ?? defaultSpacing) * 2;
      final childAspectRatio =
          _calculateChildAspectRatio(availableWidth, itemHeight);

      return DialogWrapper(
        backgroundColor: Colors.transparent,
        alignment: Alignment.topCenter,
        insetPadding: EdgeInsets.zero,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppScreenUtils.setRadius(20)),
          bottomRight: Radius.circular(AppScreenUtils.setRadius(20)),
        ),
        blurSigma: 4,
        child: Container(
          padding: const EdgeInsets.only(bottom: 1, left: 1, right: 1),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(AppScreenUtils.setRadius(20)),
              bottomRight: Radius.circular(AppScreenUtils.setRadius(20)),
            ),
          border: Border(
            bottom: BorderSide(
              color: const Color(0xff999999),
              width: 0.5.h,
              strokeAlign: BorderSide.strokeAlignInside,
            ),
            left: BorderSide(
              color: const Color(0xff999999),
              width: 0.5.w,
              strokeAlign: BorderSide.strokeAlignInside,
            ),
            right: BorderSide(
              color: const Color(0xff999999),
              width: 0.5.w,
              strokeAlign: BorderSide.strokeAlignInside,
            ),
          ),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white.withValues(alpha: 0.2),
              Colors.white.withValues(alpha: 0.4),
            ],
            ),
          ),
          child: SafeArea(
            minimum: EdgeInsets.only(
              top: 15.h,
            ),
            bottom: false,
            child: GridView.builder(
              padding: EdgeInsets.only(
                bottom: itemSpacing ?? defaultSpacing,
                left: itemSpacing ?? defaultSpacing,
                right: itemSpacing ?? defaultSpacing,
              ),
              itemCount: actions.length,
              shrinkWrap: true,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                childAspectRatio: childAspectRatio,
                crossAxisSpacing: itemSpacing ?? defaultSpacing,
                mainAxisSpacing: itemSpacing ?? defaultSpacing,
              ),
              itemBuilder: (context, index) {
                final action = actions[index];
                return _buildActionItem(
                  action: action,
                  itemSize: itemSize ?? defaultItemSize,
                  spacing: itemSpacing,
                );
              },
            ),
          ),
        ),
      );
    }
    );
  }

  Widget _buildActionItem({
    required ActionItem action,
    required double itemSize,
    double? spacing,
  }) {
    return GestureDetector(
      onTap: action.onPressed,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (action.icon != null)
            SizedBox(
              width: itemSize,
              height: itemSize,
              child: action.icon!,
            )
          else
            SizedBox(
              width: itemSize,
              height: itemSize,
            ),
          SizedBox(height: spacing),
          Text(
            action.text,
            style: itemTextStyle?.copyWith(color: itemTextColor) ??
                TextStyle(
                  fontSize: 10.sp,
                  color: itemTextColor,
                  letterSpacing: 0,
                ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
