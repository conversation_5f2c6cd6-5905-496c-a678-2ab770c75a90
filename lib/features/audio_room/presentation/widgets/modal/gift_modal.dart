import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/avatar_with_frame.dart';
import 'package:flutter_audio_room/core/widgets/image_widget.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/room_users_provider.dart';
import 'package:flutter_audio_room/features/authentication/data/model/profile_model.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/gen/assets.gen.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/bag_gift_model.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/gift_model.dart';
import 'package:flutter_audio_room/services/gift_service/presentation/provider/gift_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class LinkedTabPageView extends StatefulWidget {
  final PageController pageController;
  final TabController tabController;
  final int itemCount;
  final Widget Function(BuildContext, int) itemBuilder;
  final bool isFirstTab;
  final bool isLastTab;
  final Function()? onLoadMore;

  const LinkedTabPageView({
    super.key,
    required this.pageController,
    required this.tabController,
    required this.itemCount,
    required this.itemBuilder,
    required this.isFirstTab,
    required this.isLastTab,
    this.onLoadMore,
  });

  @override
  State<LinkedTabPageView> createState() => _LinkedTabPageViewState();
}

class _LinkedTabPageViewState extends State<LinkedTabPageView>
    with AutomaticKeepAliveClientMixin {
  bool _isAnimating = false;
  bool _isLoading = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    widget.pageController.addListener(_onPageScroll);
  }

  @override
  void dispose() {
    widget.pageController.removeListener(_onPageScroll);
    super.dispose();
  }

  void _onPageScroll() {
    if (_isAnimating || _isLoading) return;

    final position = widget.pageController.position;
    final page = widget.pageController.page?.round() ?? 0;
    final isAtStart = page == 0;
    final isAtEnd = page == widget.itemCount - 1;

    // handle scroll to previous tab
    if (!widget.isFirstTab && isAtStart) {
      final underscroll = position.pixels;
      if (underscroll < 0) {
        if (underscroll < -50 &&
            !_isAnimating &&
            widget.tabController.index > 0) {
          _isAnimating = true;
          final targetIndex = (widget.tabController.index - 1)
              .clamp(0, widget.tabController.length - 1);
          widget.tabController.animateTo(targetIndex);
          Future.delayed(const Duration(milliseconds: 300), () {
            _isAnimating = false;
          });
        }
      }
    }

    // handle scroll to next tab
    if (!widget.isLastTab && isAtEnd) {
      final maxScrollExtent = position.maxScrollExtent;
      final currentScrollExtent = position.pixels;
      final overscroll = currentScrollExtent - maxScrollExtent;

      if (overscroll > 0) {
        if (overscroll > 50 &&
            !_isAnimating &&
            widget.tabController.index < widget.tabController.length - 1) {
          _isAnimating = true;
          final targetIndex = (widget.tabController.index + 1)
              .clamp(0, widget.tabController.length - 1);
          widget.tabController.animateTo(targetIndex);
          Future.delayed(const Duration(milliseconds: 300), () {
            _isAnimating = false;
          });
        }
      }
    }

    // handle load more data
    if (isAtEnd && position.pixels >= position.maxScrollExtent) {
      if (widget.onLoadMore != null && !_isLoading) {
        _isLoading = true;
        widget.onLoadMore!().then((_) {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      children: [
        Expanded(
          child: Builder(builder: (context) {
            if (widget.itemCount <= 0) {
              return _buildEmpty();
            }
            return PageView.builder(
              itemCount: widget.itemCount,
              allowImplicitScrolling: true,
              controller: widget.pageController,
              physics: const BouncingScrollPhysics(),
              itemBuilder: widget.itemBuilder,
            );
          }
          ),
        ),
        10.verticalSpace,
        if (widget.itemCount > 1)
          SmoothPageIndicator(
            controller: widget.pageController,
            count: widget.itemCount,
            effect: ExpandingDotsEffect(
              dotHeight: AppScreenUtils.setHeight(5),
              dotWidth: AppScreenUtils.setWidth(5),
              spacing: AppScreenUtils.setWidth(6),
            ),
          ),
      ],
    );
  }

  Widget _buildEmpty() {
    return Center(
      child: Text(
        'Empty',
        style: TextStyle(
          fontSize: 16,
          color: context.colorScheme.onSurface,
        ),
      ),
    );
  }
}

class GiftModal extends ConsumerStatefulWidget {
  const GiftModal({
    super.key,
    required this.onSend,
    required this.onSendBag,
    this.selectedUser,
    this.isAudioRoom = false,
    this.initialTabIndex = 0,
  });

  final Future<void> Function(GiftModel gift, List<String> selectedUserIds)
      onSend;
  final Future<void> Function(BagGiftModel gift, String recipientUserId)
      onSendBag;
  final ProfileModel? selectedUser;
  final bool isAudioRoom;
  final int initialTabIndex;

  @override
  ConsumerState<GiftModal> createState() => _GiftModalState();
}

class _GiftModalState extends ConsumerState<GiftModal>
    with SingleTickerProviderStateMixin {
  late final PageController _giftsPageController;
  late final PageController _framePageController;
  late final PageController _bagPageController;
  late final TabController _tabController;

  final int itemsPerPage = 8;

  GiftModel? _selectedGift;
  GiftModel? _selectedFrame;
  BagGiftModel? _selectedBagGift;
  final List<String> _selectedUserList = [];
  bool _allowSelectMultipleUser = true;

  @override
  void initState() {
    super.initState();
    _giftsPageController = PageController(keepPage: true);
    _framePageController = PageController(keepPage: true);
    _bagPageController = PageController(keepPage: true);
    _tabController = TabController(
      length: 3,
      initialIndex: widget.initialTabIndex,
      vsync: this,
    );
    _tabController.addListener(() {
      if (_tabController.index == 0) {
        _allowSelectMultipleUser = true;
      } else {
        _allowSelectMultipleUser = false;
      }
      setState(() {
        _selectedUserList.clear();
      });
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _initGiftList();
    });
  }

  @override
  void dispose() {
    _giftsPageController.dispose();
    _framePageController.dispose();
    _bagPageController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initGiftList() async {
    ref
        .read(giftStateNotifierProvider.notifier)
        .refreshGiftList();
    ref
        .read(giftStateNotifierProvider.notifier)
        .refreshFrameList();
    ref
        .read(giftStateNotifierProvider.notifier)
        .refreshBagFrameList();
    ref
        .read(giftStateNotifierProvider.notifier)
        .refreshGiftList();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: EdgeInsets.only(
        bottom: 10.h,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: AppScreenUtils.setWidth(16),
        ).copyWith(top: AppScreenUtils.setHeight(10)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.selectedUser != null)
              _buildSelectedUser()
            else
              _userListToSelect(),
            12.verticalSpace,
            _buildTabBar(),
            12.verticalSpace,
            _buildTabBarView(),
            10.verticalSpace,
            _actions(),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      dividerHeight: 0,
      isScrollable: true,
      tabAlignment: TabAlignment.center,
      labelPadding: EdgeInsets.symmetric(
        vertical: AppScreenUtils.setHeight(8),
        horizontal: AppScreenUtils.setWidth(10),
      ),
      labelStyle: TextStyle(fontSize: AppScreenUtils.setFontSize(18)),
      tabs: const [
        Text('Gifts'),
        Text('Frames'),
        Text('Bag'),
      ],
    );
  }

  Widget _buildTabBarView() {
    final totalGiftItems = ref
        .watch(giftStateNotifierProvider.select((state) => state.gifts.records))
        .length;
    final totalGiftPages =
        totalGiftItems <= 0 ? 0 : (totalGiftItems / itemsPerPage).ceil();
    final totalFrameItems = ref
        .watch(
            giftStateNotifierProvider.select((state) => state.frames.records))
        .length;
    final totalFramePages =
        totalFrameItems <= 0 ? 0 : (totalFrameItems / itemsPerPage).ceil();
    final totalBagItems = ref
        .watch(giftStateNotifierProvider
            .select((state) => state.bagFrames.records))
        .length;
    final totalBagPages =
        totalBagItems <= 0 ? 0 : (totalBagItems / itemsPerPage).ceil();

    return SizedBox(
      height: AppScreenUtils.setHeight(245),
      child: TabBarView(
        controller: _tabController,
        children: [
          LinkedTabPageView(
            pageController: _giftsPageController,
            tabController: _tabController,
            itemCount: totalGiftPages,
            isFirstTab: true,
            isLastTab: false,
            onLoadMore: () async {
              await ref.read(giftStateNotifierProvider.notifier).getGiftList();
            },
            itemBuilder: (context, pageIndex) => _buildGiftGrid(pageIndex),
          ),
          LinkedTabPageView(
            pageController: _framePageController,
            tabController: _tabController,
            itemCount: totalFramePages,
            isFirstTab: false,
            isLastTab: false,
            onLoadMore: () async {
              await ref.read(giftStateNotifierProvider.notifier).getFrameList();
            },
            itemBuilder: (context, pageIndex) => _buildFrameGrid(pageIndex),
          ),
          LinkedTabPageView(
            pageController: _bagPageController,
            tabController: _tabController,
            itemCount: totalBagPages,
            isFirstTab: false,
            isLastTab: true,
            onLoadMore: () async {
              await ref
                  .read(giftStateNotifierProvider.notifier)
                  .getBagFrameList();
            },
            itemBuilder: (context, pageIndex) => _buildBagGrid(pageIndex),
          ),
        ],
      ),
    );
  }

  Widget _buildGiftGrid(int pageIndex) {
    final totalGiftItems = ref
        .watch(
        giftStateNotifierProvider.select((state) => state.gifts.records));
    final totalGiftItemsCount = totalGiftItems.length;
    final totalGiftPages =
        totalGiftItemsCount <= 0
        ? 0
        : (totalGiftItemsCount / itemsPerPage).ceil();
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      itemCount: pageIndex == totalGiftPages - 1
          ? totalGiftItemsCount % itemsPerPage == 0
              ? itemsPerPage
              : totalGiftItemsCount % itemsPerPage
          : itemsPerPage,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        childAspectRatio: 0.7,
      ),
      itemBuilder: (context, index) {
        final int actualIndex = pageIndex * itemsPerPage + index;
        if (actualIndex >= totalGiftItemsCount) return const SizedBox();

        final item = totalGiftItems[actualIndex];
        final isSelected = _selectedGift?.id == item.id;

        return Container(
          padding: EdgeInsets.symmetric(
            vertical: AppScreenUtils.setHeight(5),
          ),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  isSelected ? context.theme.primaryColor : Colors.transparent,
            ),
            borderRadius: BorderRadius.circular(AppScreenUtils.setRadius(8)),
          ),
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              setState(() {
                _selectedGift = item;
              });
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  width: AppScreenUtils.setWidth(50),
                  height: AppScreenUtils.setHeight(50),
                  child: ImageWidget(
                    source: ImageSource.network,
                    imagePath: item.imageUrl ?? '',
                  ),
                ),
                const Text(
                  'Flying Whale',
                  style: TextStyle(fontSize: 10),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),
                const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.star,
                      size: 10,
                    ),
                    Text(
                      '100',
                      style: TextStyle(fontSize: 10),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFrameGrid(int pageIndex) {
    final totalFrameItems =
        ref.watch(
        giftStateNotifierProvider.select((state) => state.frames.records));
    final totalFrameItemsCount = totalFrameItems.length;
    final totalFramePages =
        totalFrameItemsCount <= 0
        ? 0
        : (totalFrameItemsCount / itemsPerPage).ceil();
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      itemCount: pageIndex == totalFramePages - 1
          ? totalFrameItemsCount % itemsPerPage == 0
              ? itemsPerPage
              : totalFrameItemsCount % itemsPerPage
          : itemsPerPage,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        childAspectRatio: 0.7,
      ),
      itemBuilder: (context, index) {
        final int actualIndex = pageIndex * itemsPerPage + index;
        if (actualIndex >= totalFrameItemsCount) return const SizedBox();
        final item = totalFrameItems[actualIndex];
        final isSelected = _selectedFrame?.id == item.id;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedFrame = item;
            });
          },
          child: Container(
            padding: EdgeInsets.symmetric(
              vertical: AppScreenUtils.setHeight(5),
            ),
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected
                    ? context.theme.primaryColor
                    : Colors.transparent,
              ),
            ),
            child: Column(
              children: [
                SizedBox(
                  width: AppScreenUtils.setWidth(50),
                  height: AppScreenUtils.setHeight(50),
                  child: ImageWidget.network(
                    url: item.imageUrl ?? '',
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBagGrid(int pageIndex) {
    final totalBagItems = ref.watch(
        giftStateNotifierProvider.select((state) => state.bagFrames.records));
    final totalBagItemsCount = totalBagItems.length;
    final totalBagPages = totalBagItemsCount <= 0
        ? 0
        : (totalBagItemsCount / itemsPerPage).ceil();
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      itemCount: pageIndex == totalBagPages - 1
          ? totalBagItemsCount % itemsPerPage == 0
              ? itemsPerPage
              : totalBagItemsCount % itemsPerPage
          : itemsPerPage,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        childAspectRatio: 0.7,
      ),
      itemBuilder: (context, index) {
        final int actualIndex = pageIndex * itemsPerPage + index;
        if (actualIndex >= totalBagItemsCount) return const SizedBox();
        final item = totalBagItems[actualIndex];

        final isSelected = _selectedBagGift?.id == item.id;

        return Container(
          padding: EdgeInsets.symmetric(
            vertical: AppScreenUtils.setHeight(5),
          ),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  isSelected ? context.theme.primaryColor : Colors.transparent,
            ),
            borderRadius: BorderRadius.circular(AppScreenUtils.setRadius(8)),
          ),
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              setState(() {
                _selectedBagGift = item;
              });
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  width: AppScreenUtils.setWidth(50),
                  height: AppScreenUtils.setHeight(50),
                  child: ImageWidget(
                    source: ImageSource.network,
                    imagePath: item.imageUrl ?? '',
                  ),
                ),
                Text(
                  item.name ?? '',
                  style: const TextStyle(fontSize: 10),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.star,
                      size: 10,
                    ),
                    Text(
                      '${item.expiryDay} days',
                      style: const TextStyle(fontSize: 10),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
                Text(
                  item.expiresInTime,
                  style: const TextStyle(fontSize: 8),
                  textAlign: TextAlign.center,
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _actions() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppScreenUtils.setWidth(16)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppScreenUtils.setWidth(10),
              vertical: AppScreenUtils.setHeight(5),
            ),
            decoration: BoxDecoration(
              color: Colors.black12,
              borderRadius:
                  BorderRadius.circular(AppScreenUtils.setRadius(999)),
            ),
            child: Consumer(
              builder: (context, ref, child) {
                final wallet =
                    ref.watch(accountProvider.select((state) => state.wallet));
                final gems = wallet?.gems ?? 0;

                return Row(
                  children: [
                    Assets.images.ghostCoin.image(
                      width: 20.w,
                      height: 20.w,
                    ),
                    5.horizontalSpace,
                    Text('$gems'),
                  ],
                );
              },
            ),
          ),
          AppButton(
            onPressed: () {
              if (_selectedGift == null && _tabController.index == 0) {
                LoadingUtils.showToast('Please select a gift',
                    dismissOnTap: true);
                return;
              }
              if (_selectedFrame == null && _tabController.index == 1) {
                LoadingUtils.showToast('Please select a frame',
                    dismissOnTap: true);
                return;
              }
              if (_selectedBagGift == null && _tabController.index == 2) {
                LoadingUtils.showToast('Please select a bag gift',
                    dismissOnTap: true);
                return;
              }
              List<String> userIdList = [];

              if (widget.selectedUser != null) {
                userIdList.add(widget.selectedUser!.id!);
              } else {
                userIdList.addAll(_selectedUserList);
              }

              if (userIdList.isEmpty) {
                LoadingUtils.showToast('Please select a user',
                    dismissOnTap: true);
                return;
              }

              if (_tabController.index == 0) {
                widget.onSend(
                  _selectedGift!,
                  userIdList,
                );
              } else if (_tabController.index == 1) {
                widget.onSend(
                  _selectedFrame!,
                  userIdList,
                );
              } else {
                widget.onSendBag(
                  _selectedBagGift!,
                  userIdList.first,
                );
              }
            },
            text: 'Send',
            textStyle: (context, style) => style.copyWith(fontSize: 15),
            width: AppScreenUtils.setWidth(100),
            borderRadius: AppScreenUtils.setRadius(999),
          )
        ],
      ),
    );
  }

  Widget _buildSelectedUser() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppScreenUtils.setWidth(16),
        vertical: AppScreenUtils.setHeight(8),
      ),
      child: Row(
        children: [
          const Text('Send gift to'),
          AvatarWithFrame(
            avatarUrl: widget.selectedUser?.avatar ?? '',
            width: 20,
            height: 20,
          ),
          5.horizontalSpace,
          Text(widget.selectedUser?.nickName ?? ''),
        ],
      ),
    );
  }

  Widget _userListToSelect() {
    if (!widget.isAudioRoom) return const SizedBox.shrink();

    return Consumer(builder: (context, ref, child) {
      final userList = ref.watch(roomOnMicProvider);
      final List<Widget> userWidgets = [];
      for (var i = 0; i < userList.length; i++) {
        final isSelected = _selectedUserList.contains(userList[i].userId!);
        userWidgets.add(Column(
          children: [
            GestureDetector(
              onTap: () {
                setState(() {
                  if (_allowSelectMultipleUser) {
                    if (isSelected) {
                      _selectedUserList.remove(userList[i].userId!);
                    } else {
                      _selectedUserList.add(userList[i].userId!);
                    }
                  } else {
                    _selectedUserList.clear();
                    _selectedUserList.add(userList[i].userId!);
                  }
                });
              },
              child: Container(
                padding: EdgeInsets.all(AppScreenUtils.setWidth(10)),
                decoration: BoxDecoration(
                  color: Colors.black12,
                  border: Border.all(
                    color: isSelected
                        ? context.theme.primaryColor
                        : Colors.transparent,
                  ),
                  borderRadius:
                      BorderRadius.circular(AppScreenUtils.setRadius(999)),
                ),
                child: const Icon(Icons.person),
              ),
            ),
            Text(userList[i].firstName ?? ''),
          ],
        ));
        if (i < userList.length - 1) {
          userWidgets.add(10.horizontalSpace);
        }
      }
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: userWidgets,
        ),
      );
    });
  }
}
