import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_mini_player_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'audio_room_wrapper/audio_room_wrapper_animation_controller.dart';
import 'audio_room_wrapper/audio_room_wrapper_ui_builder.dart';

class CommonMinimizeWrapper extends ConsumerStatefulWidget {
  final bool fromMiniPlayer;
  final Route route;
  final Widget Function(
    BuildContext context,
    Future<void> Function() onClose,
    Future<void> Function<T>(Route<T> routerConfig) onMinimize,
  ) builder;

  const CommonMinimizeWrapper({
    super.key,
    this.fromMiniPlayer = false,
    required this.builder,
    required this.route,
  });

  @override
  ConsumerState<CommonMinimizeWrapper> createState() =>
      _CommonMinisizeableWrapperState();
}

class _CommonMinisizeableWrapperState
    extends ConsumerState<CommonMinimizeWrapper> with TickerProviderStateMixin {
  late final AudioRoomWrapperAnimationController _animationController;
  bool _isClosing = false;

  @override
  void initState() {
    super.initState();
    _animationController = AudioRoomWrapperAnimationController(vsync: this);
    _setupPostFrameCallback();
  }

  void _setupPostFrameCallback() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _initializeAnimations();
      _handleInitialAnimation();
      getIt<IMiniPlayerService>().removeMiniPlayer();
    });
  }

  void _handleInitialAnimation() async {
    if (widget.fromMiniPlayer) {
      // 从小球展开到全屏：从 0.0 (小球) 动画到 1.0 (全屏)
      _animationController.minimizeController.value = 0.0;
      await _animationController.minimizeController.forward();
    } else {
      // 直接显示全屏
      _animationController.minimizeController.value = 1.0;
    }
  }

  void _initializeAnimations() {
    final screenSize = MediaQuery.of(context).size;
    final miniPlayerPosition = getIt<IMiniPlayerService>().miniPlayerPosition;
    _animationController.initializePositionAnimation(
        screenSize, miniPlayerPosition);
  }

  Future<void> _handleMinimize<T>(Route<T> router) async {
    _isClosing = false;
    // 重新初始化动画以获取最新的小球位置
    _initializeAnimations();

    // 从全屏 (1.0) 缩小到小球 (0.0)
    await _animationController.minimizeController.reverse();

    if (mounted) {
      getIt<IMiniPlayerService>().showMiniPlayer(context, router);
    }

    // 稍微延迟后关闭页面
    await Future.delayed(const Duration(milliseconds: 100));
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  Future<void> _handleClose() async {
    _isClosing = true;
    await _animationController.closeController.forward();
    
    if (mounted) {
      context.pop();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _initializeAnimations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _isClosing && _animationController.closeController.isCompleted,
      onPopInvokedWithResult: (bool didPop, _) async {
        if (didPop) return;
        await _handleClose();
      },
      child: Material(
        type: MaterialType.transparency,
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _animationController.minimizeController,
            _animationController.closeController,
          ]),
          builder: (context, child) {
            if (_isClosing) {
              return SlideTransition(
                position: _animationController.slideAnimation,
                child: _buildContent(context),
              );
            }
            return _buildContent(context);
          },
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return AudioRoomWrapperUIBuilder.buildAnimatedContent(
      context,
      _animationController,
      (radius) => AudioRoomWrapperUIBuilder.buildContainer(
        context,
        _animationController,
        widget.builder(context, _handleClose, _handleMinimize),
        radius,
      ),
    );
  }
}
