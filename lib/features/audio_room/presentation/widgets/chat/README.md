# 语音房间文字聊天功能

基于 Agora RTM Stream Channel 实现的语音房间文字聊天功能，支持实时文字消息发送和接收，以及系统消息的整合显示。

## 功能特性

### ✅ 已实现功能
- **纯文字消息发送和接收**：支持实时文字聊天
- **系统消息整合**：将房间提示消息（用户进入、离开等）整合到聊天系统中
- **消息类型分类**：支持文字消息、系统消息、通知消息三种类型
- **实时消息流**：基于 Stream 的实时消息处理
- **UI 组件完整**：提供消息列表、输入框、聊天面板等完整 UI 组件
- **状态管理**：基于 Riverpod 的状态管理
- **消息持久化**：内存中的消息存储和管理

### 🚧 待实现功能
- 表情包支持
- 图片消息支持
- 消息历史记录持久化
- 消息撤回功能
- @用户功能

## 技术架构

### 核心组件

1. **ChatMessageModel** - 聊天消息数据模型
2. **ChatService** - 聊天服务核心类
3. **ChatMessageStream** - 消息流管理
4. **ChatMessageHandler** - 消息处理器
5. **RtmStreamChannelMixin** - RTM Stream Channel 管理

### 消息流程

```
用户输入 → ChatService → RTM Stream Channel → 其他用户接收
                ↓
        ChatMessageStream → UI 更新
```

### 消息类型

- **ChatMessageType.text** - 普通文字消息
- **ChatMessageType.system** - 系统消息（用户进入/离开等）
- **ChatMessageType.notification** - 通知消息（操作提示等）

## 使用方法

### 1. 基本集成

```dart
// 1. 初始化聊天服务
final audioRoomService = AudioRoomService(
  rtcService: rtcService,
  rtmService: rtmService,
);

// 2. 初始化聊天功能
await audioRoomService.initializeChatService();

// 3. 监听消息流
audioRoomService.chatMessageStream?.listen((message) {
  // 处理接收到的消息
  ref.read(chatMessageProvider.notifier).addMessage(message);
});
```

### 2. 发送消息

```dart
// 发送文字消息
await audioRoomService.sendChatMessage(
  content: '你好，大家好！',
  senderId: currentUserId,
  senderName: currentUserName,
  senderAvatar: currentUserAvatar,
  roomId: roomId,
);

// 添加系统消息
audioRoomService.addSystemMessage('用户张三加入了房间');
```

### 3. UI 组件使用

```dart
// 使用完整聊天面板
ChatPanel(
  onSendMessage: (message) async {
    await audioRoomService.sendChatMessage(
      content: message,
      senderId: currentUserId,
      senderName: currentUserName,
    );
  },
  showSystemMessages: true,
  enabled: true,
)

// 使用消息列表
ChatMessageList(
  showSystemMessages: true,
  autoScroll: true,
)

// 使用输入框
ChatInputField(
  onSendMessage: (message) {
    // 处理发送消息
  },
  hintText: '说点什么...',
  maxLength: 200,
)
```

### 4. 系统消息整合

```dart
// 处理房间事件并转换为聊天消息
void handleRoomEvent(RoomMessageModel roomMessage) {
  audioRoomService.handleRoomMessageForChat(roomMessage);
}

// 手动添加系统消息
ChatIntegrationHelper.handleRoomEvent(
  audioRoomService: audioRoomService,
  eventType: 'user_join',
  userName: '张三',
  roomId: roomId,
);
```

## 文件结构

```
lib/features/audio_room/
├── data/model/chat_message/
│   └── chat_message_model.dart          # 聊天消息模型
├── domain/services/
│   └── chat_message_stream.dart         # 消息流服务
├── presentation/
│   ├── handlers/message/
│   │   └── chat_message_handler.dart    # 消息处理器
│   ├── providers/
│   │   └── chat_message_provider.dart   # 状态管理
│   ├── services/
│   │   ├── chat_service.dart            # 聊天服务
│   │   └── rtm/mixins/
│   │       └── rtm_stream_channel_mixin.dart  # RTM Stream Channel 管理
│   └── widgets/chat/
│       ├── chat_panel.dart              # 聊天面板
│       ├── chat_message_list.dart       # 消息列表
│       ├── chat_message_item.dart       # 消息项
│       ├── chat_input_field.dart        # 输入框
│       └── chat_integration_example.dart # 集成示例
```

## 配置说明

### RTM Stream Channel 配置

- **Topic 名称**: `text_chat`
- **消息大小限制**: 1KB
- **QoS**: `ordered` (有序消息)
- **消息类型**: Binary (JSON 编码)

### 消息格式

```json
{
  "id": "1640995200000",
  "content": "你好，大家好！",
  "senderId": 12345,
  "senderName": "张三",
  "senderAvatar": "https://example.com/avatar.jpg",
  "timestamp": 1640995200000,
  "type": "text",
  "roomId": "room_123",
  "extra": {}
}
```

## 注意事项

1. **消息大小限制**: Stream Channel 限制单条消息最大 1KB
2. **网络依赖**: 需要稳定的网络连接
3. **权限管理**: 确保用户有发送消息的权限
4. **资源清理**: 离开房间时需要正确清理聊天服务资源
5. **错误处理**: 实现适当的错误处理和重试机制

## 性能优化

1. **消息缓存**: 限制内存中保存的消息数量
2. **UI 优化**: 使用虚拟列表处理大量消息
3. **网络优化**: 实现消息队列和重试机制
4. **内存管理**: 及时清理不需要的消息和监听器

## 故障排除

### 常见问题

1. **消息发送失败**
   - 检查网络连接
   - 确认 RTM 服务已正确初始化
   - 验证用户权限

2. **消息接收不到**
   - 检查 Topic 订阅状态
   - 确认事件监听器设置正确
   - 验证消息格式

3. **UI 不更新**
   - 检查 Riverpod Provider 状态
   - 确认消息流监听正常
   - 验证 Widget 重建逻辑

### 调试建议

1. 启用详细日志输出
2. 检查 RTM 连接状态
3. 验证消息序列化/反序列化
4. 监控内存使用情况
