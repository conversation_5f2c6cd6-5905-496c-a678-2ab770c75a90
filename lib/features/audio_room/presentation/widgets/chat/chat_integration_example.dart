import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/chat_message_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/services/audio_room_service.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/chat_panel.dart';

/// 聊天功能集成示例
/// 展示如何在语音房间中集成文字聊天功能
class ChatIntegrationExample extends ConsumerStatefulWidget {
  final AudioRoomService audioRoomService;
  final int currentUserId;
  final String currentUserName;
  final String? currentUserAvatar;
  final String? roomId;

  const ChatIntegrationExample({
    super.key,
    required this.audioRoomService,
    required this.currentUserId,
    required this.currentUserName,
    this.currentUserAvatar,
    this.roomId,
  });

  @override
  ConsumerState<ChatIntegrationExample> createState() => _ChatIntegrationExampleState();
}

class _ChatIntegrationExampleState extends ConsumerState<ChatIntegrationExample> {
  bool _isChatVisible = false;
  bool _isChatInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeChat();
    _setupMessageListeners();
  }

  /// 初始化聊天功能
  Future<void> _initializeChat() async {
    final result = await widget.audioRoomService.initializeChatService();
    if (result.isRight()) {
      setState(() {
        _isChatInitialized = true;
      });
      
      // 添加欢迎消息
      widget.audioRoomService.addSystemMessage(
        '欢迎来到语音房间！',
        roomId: widget.roomId,
      );
    }
  }

  /// 设置消息监听
  void _setupMessageListeners() {
    // 监听聊天消息流
    widget.audioRoomService.chatMessageStream?.listen((message) {
      // 将消息添加到Provider中
      ref.read(chatMessageProvider.notifier).addMessage(message);
    });

    // 监听系统消息流
    widget.audioRoomService.systemMessageStream?.listen((message) {
      ref.read(chatMessageProvider.notifier).addMessage(message);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // 主要内容区域（语音房间UI）
          _buildMainContent(),
          
          // 聊天面板
          if (_isChatVisible) _buildChatPanel(),
          
          // 聊天按钮
          _buildChatButton(),
        ],
      ),
    );
  }

  /// 构建主要内容
  Widget _buildMainContent() {
    return Container(
      color: Colors.grey[100],
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.mic,
              size: 100,
              color: Colors.blue,
            ),
            SizedBox(height: 20),
            Text(
              '语音房间',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10),
            Text(
              '点击右下角按钮打开聊天',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建聊天面板
  Widget _buildChatPanel() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.6,
        child: ChatPanel(
          onSendMessage: _handleSendMessage,
          showSystemMessages: true,
          enabled: _isChatInitialized,
          placeholder: _isChatInitialized ? '说点什么...' : '聊天功能初始化中...',
        ),
      ),
    );
  }

  /// 构建聊天按钮
  Widget _buildChatButton() {
    final messages = ref.watch(chatMessageProvider);
    final unreadCount = messages.where((m) => m.type == ChatMessageType.text).length;

    return Positioned(
      bottom: 100,
      right: 20,
      child: Stack(
        children: [
          FloatingActionButton(
            onPressed: _toggleChat,
            backgroundColor: _isChatVisible ? Colors.grey : Colors.blue,
            child: Icon(
              _isChatVisible ? Icons.close : Icons.chat,
              color: Colors.white,
            ),
          ),
          if (!_isChatVisible && unreadCount > 0)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Text(
                  unreadCount > 99 ? '99+' : unreadCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 处理发送消息
  Future<void> _handleSendMessage(String message) async {
    if (!_isChatInitialized) return;

    final result = await widget.audioRoomService.sendChatMessage(
      content: message,
      senderId: widget.currentUserId,
      senderName: widget.currentUserName,
      senderAvatar: widget.currentUserAvatar,
      roomId: widget.roomId,
    );

    if (result.isLeft()) {
      // 显示错误消息
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('发送消息失败'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 切换聊天面板显示状态
  void _toggleChat() {
    setState(() {
      _isChatVisible = !_isChatVisible;
    });
  }

  /// 模拟用户进入房间
  void _simulateUserJoin(String userName) {
    widget.audioRoomService.addSystemMessage(
      '$userName 加入了房间',
      roomId: widget.roomId,
    );
  }

  /// 模拟用户离开房间
  void _simulateUserLeave(String userName) {
    widget.audioRoomService.addSystemMessage(
      '$userName 离开了房间',
      roomId: widget.roomId,
    );
  }

  /// 模拟系统通知
  void _simulateSystemNotification(String content) {
    widget.audioRoomService.addSystemMessage(
      content,
      roomId: widget.roomId,
    );
  }

  @override
  void dispose() {
    // 清理资源在AudioRoomService中处理
    super.dispose();
  }
}

/// 聊天集成帮助类
class ChatIntegrationHelper {
  /// 创建聊天集成示例
  static Widget createExample({
    required AudioRoomService audioRoomService,
    required int currentUserId,
    required String currentUserName,
    String? currentUserAvatar,
    String? roomId,
  }) {
    return ChatIntegrationExample(
      audioRoomService: audioRoomService,
      currentUserId: currentUserId,
      currentUserName: currentUserName,
      currentUserAvatar: currentUserAvatar,
      roomId: roomId,
    );
  }

  /// 处理房间事件并转换为聊天消息
  static void handleRoomEvent({
    required AudioRoomService audioRoomService,
    required String eventType,
    required String userName,
    String? roomId,
  }) {
    String message;
    switch (eventType) {
      case 'user_join':
        message = '$userName 加入了房间';
        break;
      case 'user_leave':
        message = '$userName 离开了房间';
        break;
      case 'user_mute':
        message = '$userName 被静音了';
        break;
      case 'user_unmute':
        message = '$userName 取消静音了';
        break;
      default:
        return;
    }
    
    audioRoomService.addSystemMessage(message, roomId: roomId);
  }
}
