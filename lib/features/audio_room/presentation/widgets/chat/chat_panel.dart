import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/chat_message_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/chat_input_field.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/chat_message_list.dart';

/// 聊天面板组件
/// 包含消息列表和输入框的完整聊天界面
class ChatPanel extends ConsumerStatefulWidget {
  final Function(String message) onSendMessage;
  final double? height;
  final bool showSystemMessages;
  final bool enabled;
  final String? placeholder;

  const ChatPanel({
    super.key,
    required this.onSendMessage,
    this.height,
    this.showSystemMessages = true,
    this.enabled = true,
    this.placeholder,
  });

  @override
  ConsumerState<ChatPanel> createState() => _ChatPanelState();
}

class _ChatPanelState extends ConsumerState<ChatPanel> {
  final GlobalKey<_ChatMessageListState> _messageListKey = 
      GlobalKey<_ChatMessageListState>();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(16.0),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8.0,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: ChatMessageList(
              key: _messageListKey,
              showSystemMessages: widget.showSystemMessages,
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
            ),
          ),
          ChatInputField(
            onSendMessage: _handleSendMessage,
            enabled: widget.enabled,
            hintText: widget.placeholder ?? '说点什么...',
          ),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withOpacity(0.2),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.chat_bubble_outline,
            size: 20,
            color: Colors.grey,
          ),
          const SizedBox(width: 8.0),
          const Text(
            '聊天',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          _buildMessageCount(),
        ],
      ),
    );
  }

  /// 构建消息计数
  Widget _buildMessageCount() {
    final messages = ref.watch(chatMessageProvider);
    final textMessageCount = messages
        .where((m) => m.type == ChatMessageType.text)
        .length;

    if (textMessageCount == 0) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Text(
        '$textMessageCount',
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 处理发送消息
  void _handleSendMessage(String message) {
    if (message.trim().isEmpty) return;
    
    // 调用外部回调
    widget.onSendMessage(message);
    
    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  /// 滚动到底部
  void _scrollToBottom() {
    _messageListKey.currentState?.scrollToBottom();
  }

  /// 添加系统消息（公开方法）
  void addSystemMessage(String content) {
    final message = ChatMessageModel.createSystemMessage(content: content);
    ref.read(chatMessageProvider.notifier).addMessage(message);
  }

  /// 添加通知消息（公开方法）
  void addNotificationMessage(String content) {
    final message = ChatMessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      senderId: 0,
      senderName: 'System',
      timestamp: DateTime.now().millisecondsSinceEpoch,
      type: ChatMessageType.notification,
    );
    ref.read(chatMessageProvider.notifier).addMessage(message);
  }
}

/// 聊天面板构建器
/// 提供便捷的聊天面板创建方法
class ChatPanelBuilder {
  /// 创建底部聊天面板
  static Widget bottomPanel({
    required Function(String message) onSendMessage,
    double height = 400,
    bool showSystemMessages = true,
    bool enabled = true,
    String? placeholder,
  }) {
    return ChatPanel(
      onSendMessage: onSendMessage,
      height: height,
      showSystemMessages: showSystemMessages,
      enabled: enabled,
      placeholder: placeholder,
    );
  }

  /// 创建全屏聊天面板
  static Widget fullScreenPanel({
    required Function(String message) onSendMessage,
    bool showSystemMessages = true,
    bool enabled = true,
    String? placeholder,
  }) {
    return ChatPanel(
      onSendMessage: onSendMessage,
      showSystemMessages: showSystemMessages,
      enabled: enabled,
      placeholder: placeholder,
    );
  }

  /// 创建嵌入式聊天面板
  static Widget embeddedPanel({
    required Function(String message) onSendMessage,
    required double height,
    bool showSystemMessages = false,
    bool enabled = true,
    String? placeholder,
  }) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: ChatPanel(
        onSendMessage: onSendMessage,
        height: height,
        showSystemMessages: showSystemMessages,
        enabled: enabled,
        placeholder: placeholder,
      ),
    );
  }
}
