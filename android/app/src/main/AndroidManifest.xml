<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">
  <!-- Agora -->
  <uses-permission android:name="android.permission.INTERNET"/>
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
  <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
  <uses-permission android:name="android.permission.RECORD_AUDIO"/>
  <uses-permission android:name="android.permission.CAMERA"/>
  <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
  <!-- The Agora SDK requires Bluetooth permissions in case users are using Bluetooth devices. -->
  <uses-permission android:name="android.permission.BLUETOOTH"/>
  <!-- For Android 12 and above devices, the following permission is also required. -->
  <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
  <!-- For Android 14 and above devices, the following permission is also required. -->
  <uses-permission android:name="android.permission.DETECT_SCREEN_CAPTURE"/>
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
  <uses-permission android:name="com.android.vending.BILLING"/>
  <!-- Notification -->
  <!-- Samsung -->
  <uses-permission android:name="com.sec.android.provider.badge.permission.READ"/>
  <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE"/>
  <!-- HTC -->
  <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS"/>
  <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT"/>
  <!-- Sony -->
  <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE"/>
  <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE"/>
  <!-- Apex -->
  <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT"/>
  <!-- Solid -->
  <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE"/>
  <!-- Huawei -->
  <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE"/>
  <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS"/>
  <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS"/>
  <application android:label="@string/app_name" android:name="${applicationName}" android:icon="@mipmap/ic_launcher">
    <activity android:name=".MainActivity" android:exported="true" android:launchMode="singleTop" android:taskAffinity="" android:theme="@style/LaunchTheme" android:enableOnBackInvokedCallback="false" android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode" android:hardwareAccelerated="true" android:windowSoftInputMode="adjustResize">
      <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
      <meta-data android:name="io.flutter.embedding.android.NormalTheme" android:resource="@style/NormalTheme"/>
      <intent-filter>
        <action android:name="android.intent.action.MAIN"/>
        <category android:name="android.intent.category.LAUNCHER"/>
      </intent-filter>
    </activity>
    <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
    <meta-data android:name="flutterEmbedding" android:value="2"/>
  </application>
  <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
  <queries>
    <intent>
      <action android:name="android.intent.action.PROCESS_TEXT"/>
      <data android:mimeType="text/plain"/>
    </intent>
  </queries>
</manifest>