PODS:
  - agora_rtc_engine (6.5.2):
    - AgoraIrisRTC_iOS (= 4.5.2-build.1)
    - AgoraRtcEngine_iOS (= 4.5.2)
    - Flutter
  - agora_rtm (2.2.2):
    - AgoraIrisRTM_iOS (= 2.2.1-build.1)
    - AgoraRtm (= 2.2.1)
    - Flutter
  - AgoraInfra_iOS (********)
  - AgoraIrisRTC_iOS (4.5.2-build.1)
  - AgoraIrisRTM_iOS (2.2.1-build.1)
  - AgoraRtcEngine_iOS (4.5.2):
    - AgoraRtcEngine_iOS/AIAEC (= 4.5.2)
    - AgoraRtcEngine_iOS/AIAECLL (= 4.5.2)
    - AgoraRtcEngine_iOS/AINS (= 4.5.2)
    - AgoraRtcEngine_iOS/AINSLL (= 4.5.2)
    - AgoraRtcEngine_iOS/AudioBeauty (= 4.5.2)
    - AgoraRtcEngine_iOS/ClearVision (= 4.5.2)
    - AgoraRtcEngine_iOS/ContentInspect (= 4.5.2)
    - AgoraRtcEngine_iOS/FaceCapture (= 4.5.2)
    - AgoraRtcEngine_iOS/FaceDetection (= 4.5.2)
    - AgoraRtcEngine_iOS/LipSync (= 4.5.2)
    - AgoraRtcEngine_iOS/ReplayKit (= 4.5.2)
    - AgoraRtcEngine_iOS/RtcBasic (= 4.5.2)
    - AgoraRtcEngine_iOS/SpatialAudio (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoAv1CodecDec (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoAv1CodecEnc (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoCodecDec (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoCodecEnc (= 4.5.2)
    - AgoraRtcEngine_iOS/VirtualBackground (= 4.5.2)
    - AgoraRtcEngine_iOS/VQA (= 4.5.2)
  - AgoraRtcEngine_iOS/AIAEC (4.5.2)
  - AgoraRtcEngine_iOS/AIAECLL (4.5.2)
  - AgoraRtcEngine_iOS/AINS (4.5.2)
  - AgoraRtcEngine_iOS/AINSLL (4.5.2)
  - AgoraRtcEngine_iOS/AudioBeauty (4.5.2)
  - AgoraRtcEngine_iOS/ClearVision (4.5.2)
  - AgoraRtcEngine_iOS/ContentInspect (4.5.2)
  - AgoraRtcEngine_iOS/FaceCapture (4.5.2)
  - AgoraRtcEngine_iOS/FaceDetection (4.5.2)
  - AgoraRtcEngine_iOS/LipSync (4.5.2)
  - AgoraRtcEngine_iOS/ReplayKit (4.5.2)
  - AgoraRtcEngine_iOS/RtcBasic (4.5.2):
    - AgoraInfra_iOS (= ********)
  - AgoraRtcEngine_iOS/SpatialAudio (4.5.2)
  - AgoraRtcEngine_iOS/VideoAv1CodecDec (4.5.2)
  - AgoraRtcEngine_iOS/VideoAv1CodecEnc (4.5.2)
  - AgoraRtcEngine_iOS/VideoCodecDec (4.5.2)
  - AgoraRtcEngine_iOS/VideoCodecEnc (4.5.2)
  - AgoraRtcEngine_iOS/VirtualBackground (4.5.2)
  - AgoraRtcEngine_iOS/VQA (4.5.2)
  - AgoraRtm (2.2.1)
  - app_badge_plus (1.2.3):
    - Flutter
  - audio_session (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Messaging (11.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.10.0)
  - firebase_core (3.13.0):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_messaging (15.2.5):
    - Firebase/Messaging (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_sound (9.28.0):
    - Flutter
    - flutter_sound_core (= 9.28.0)
  - flutter_sound_core (9.28.0)
  - flutter_timezone (0.0.1):
    - Flutter
  - flutter_webrtc (0.12.6):
    - Flutter
    - WebRTC-SDK (= 125.6422.06)
  - geocoding_ios (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - iris_method_channel (0.0.1):
    - Flutter
  - irondash_engine_context (0.0.1):
    - Flutter
  - isar_flutter_libs (1.0.0):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - libphonenumber_plugin (0.0.1):
    - Flutter
    - PhoneNumberKit
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PhoneNumberKit (3.8.0):
    - PhoneNumberKit/PhoneNumberKitCore (= 3.8.0)
    - PhoneNumberKit/UIKit (= 3.8.0)
  - PhoneNumberKit/PhoneNumberKitCore (3.8.0)
  - PhoneNumberKit/UIKit (3.8.0):
    - PhoneNumberKit/PhoneNumberKitCore
  - PromisesObjC (2.4.0)
  - screen_protector (1.2.1):
    - Flutter
    - ScreenProtectorKit (~> 1.3.1)
  - ScreenProtectorKit (1.3.1)
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - super_native_extensions (0.0.1):
    - Flutter
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_compress (0.3.0):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - video_thumbnail (0.0.1):
    - Flutter
    - libwebp
  - WebRTC-SDK (125.6422.06)

DEPENDENCIES:
  - agora_rtc_engine (from `.symlinks/plugins/agora_rtc_engine/ios`)
  - agora_rtm (from `.symlinks/plugins/agora_rtm/ios`)
  - app_badge_plus (from `.symlinks/plugins/app_badge_plus/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_sound (from `.symlinks/plugins/flutter_sound/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - flutter_webrtc (from `.symlinks/plugins/flutter_webrtc/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - iris_method_channel (from `.symlinks/plugins/iris_method_channel/ios`)
  - irondash_engine_context (from `.symlinks/plugins/irondash_engine_context/ios`)
  - isar_flutter_libs (from `.symlinks/plugins/isar_flutter_libs/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - libphonenumber_plugin (from `.symlinks/plugins/libphonenumber_plugin/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - PhoneNumberKit (= 3.8.0)
  - screen_protector (from `.symlinks/plugins/screen_protector/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - super_native_extensions (from `.symlinks/plugins/super_native_extensions/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_compress (from `.symlinks/plugins/video_compress/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - video_thumbnail (from `.symlinks/plugins/video_thumbnail/ios`)

SPEC REPOS:
  trunk:
    - AgoraInfra_iOS
    - AgoraIrisRTC_iOS
    - AgoraIrisRTM_iOS
    - AgoraRtcEngine_iOS
    - AgoraRtm
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - flutter_sound_core
    - GoogleDataTransport
    - GoogleUtilities
    - libwebp
    - Mantle
    - nanopb
    - PhoneNumberKit
    - PromisesObjC
    - ScreenProtectorKit
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif
    - WebRTC-SDK

EXTERNAL SOURCES:
  agora_rtc_engine:
    :path: ".symlinks/plugins/agora_rtc_engine/ios"
  agora_rtm:
    :path: ".symlinks/plugins/agora_rtm/ios"
  app_badge_plus:
    :path: ".symlinks/plugins/app_badge_plus/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_sound:
    :path: ".symlinks/plugins/flutter_sound/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  flutter_webrtc:
    :path: ".symlinks/plugins/flutter_webrtc/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  iris_method_channel:
    :path: ".symlinks/plugins/iris_method_channel/ios"
  irondash_engine_context:
    :path: ".symlinks/plugins/irondash_engine_context/ios"
  isar_flutter_libs:
    :path: ".symlinks/plugins/isar_flutter_libs/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  libphonenumber_plugin:
    :path: ".symlinks/plugins/libphonenumber_plugin/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  screen_protector:
    :path: ".symlinks/plugins/screen_protector/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  super_native_extensions:
    :path: ".symlinks/plugins/super_native_extensions/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_compress:
    :path: ".symlinks/plugins/video_compress/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  video_thumbnail:
    :path: ".symlinks/plugins/video_thumbnail/ios"

SPEC CHECKSUMS:
  agora_rtc_engine: 6a8683bd2d8473e36f059d3bac5f1f475199d571
  agora_rtm: 0c68ab58a94c6bbebb292bb26091633da0ecaf40
  AgoraInfra_iOS: 3691b2b277a1712a35ae96de25af319de0d73d08
  AgoraIrisRTC_iOS: eab58c126439adf5ec99632828a558ea216860da
  AgoraIrisRTM_iOS: a7d484dbd8632db3c520fa46b5e1ce054329dbb3
  AgoraRtcEngine_iOS: 97e2398a2addda9057815a2a583a658e36796ff6
  AgoraRtm: 0c0ea266de02ad6e4e43b1d33b7bb093bc347555
  app_badge_plus: 0e3470f993dd08094e16463f57a7e0db04c6b587
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_core: 2d4534e7b489907dcede540c835b48981d890943
  firebase_messaging: 75bc93a4df25faccad67f6662ae872ac9ae69b64
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseMessaging: 2b9f56aa4ed286e1f0ce2ee1d413aabb8f9f5cb9
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_local_notifications: a5a732f069baa862e728d839dd2ebb904737effb
  flutter_native_splash: 6cad9122ea0fad137d23137dd14b937f3e90b145
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  flutter_sound: b9236a5875299aaa4cef1690afd2f01d52a3f890
  flutter_sound_core: 427465f72d07ab8c3edbe8ffdde709ddacd3763c
  flutter_timezone: ee50ce7786b5fde27e2fe5375bbc8c9661ffc13f
  flutter_webrtc: 57f32415b8744e806f9c2a96ccdb60c6a627ba33
  geocoding_ios: bcbdaa6bddd7d3129c9bcb8acddc5d8778689768
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  in_app_purchase_storekit: d1a48cb0f8b29dbf5f85f782f5dd79b21b90a5e6
  iris_method_channel: b9db2053dac3dc84e256c8792eff6f11323a53bd
  irondash_engine_context: 8e58ca8e0212ee9d1c7dc6a42121849986c88486
  isar_flutter_libs: bc909e72c3d756c2759f14c8776c13b5b0556e26
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  libphonenumber_plugin: d134f173b22bfa5ede50887071f087f309277f8c
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PhoneNumberKit: ec00ab8cef5342c1dc49fadb99d23fa7e66cf0ef
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  screen_protector: 3d90d44ac886b25335aebd93230b454aef45794a
  ScreenProtectorKit: 83a6281b02c7a5902ee6eac4f5045f674e902ae4
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  super_native_extensions: b763c02dc3a8fd078389f410bf15149179020cb4
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_compress: f2133a07762889d67f0711ac831faa26f956980e
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  video_thumbnail: b637e0ad5f588ca9945f6e2c927f73a69a661140
  WebRTC-SDK: 79942c006ea64f6fb48d7da8a4786dfc820bc1db

PODFILE CHECKSUM: 8fdae9fbef534e307784130d3ebab1cde06419a0

COCOAPODS: 1.16.2
