import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';

void main() {
  group('ChatMessageModel Tests', () {
    test('should create text message correctly', () {
      // Arrange
      const content = 'Hello, world!';
      const senderId = 12345;
      const senderName = '<PERSON>';
      const senderAvatar = 'https://example.com/avatar.jpg';
      const roomId = 'room_123';

      // Act
      final message = ChatMessageModel.createTextMessage(
        content: content,
        senderId: senderId,
        senderName: senderName,
        senderAvatar: senderAvatar,
        roomId: roomId,
      );

      // Assert
      expect(message.content, equals(content));
      expect(message.senderId, equals(senderId));
      expect(message.senderName, equals(senderName));
      expect(message.senderAvatar, equals(senderAvatar));
      expect(message.roomId, equals(roomId));
      expect(message.type, equals(ChatMessageType.text));
      expect(message.isTextMessage, isTrue);
      expect(message.isSystemMessage, isFalse);
      expect(message.isNotificationMessage, isFalse);
    });

    test('should create system message correctly', () {
      // Arrange
      const content = 'User joined the room';
      const roomId = 'room_123';

      // Act
      final message = ChatMessageModel.createSystemMessage(
        content: content,
        roomId: roomId,
      );

      // Assert
      expect(message.content, equals(content));
      expect(message.senderId, equals(0));
      expect(message.senderName, equals('System'));
      expect(message.roomId, equals(roomId));
      expect(message.type, equals(ChatMessageType.system));
      expect(message.isSystemMessage, isTrue);
      expect(message.isTextMessage, isFalse);
      expect(message.isNotificationMessage, isFalse);
    });

    test('should serialize and deserialize correctly', () {
      // Arrange
      final originalMessage = ChatMessageModel.createTextMessage(
        content: 'Test message',
        senderId: 12345,
        senderName: 'Test User',
        senderAvatar: 'https://example.com/avatar.jpg',
        roomId: 'room_123',
        extra: {'key': 'value'},
      );

      // Act
      final json = originalMessage.toJson();
      final deserializedMessage = ChatMessageModel.fromJson(json);

      // Assert
      expect(deserializedMessage.id, equals(originalMessage.id));
      expect(deserializedMessage.content, equals(originalMessage.content));
      expect(deserializedMessage.senderId, equals(originalMessage.senderId));
      expect(deserializedMessage.senderName, equals(originalMessage.senderName));
      expect(deserializedMessage.senderAvatar, equals(originalMessage.senderAvatar));
      expect(deserializedMessage.timestamp, equals(originalMessage.timestamp));
      expect(deserializedMessage.type, equals(originalMessage.type));
      expect(deserializedMessage.roomId, equals(originalMessage.roomId));
      expect(deserializedMessage.extra, equals(originalMessage.extra));
    });

    test('should handle JSON with missing optional fields', () {
      // Arrange
      final json = {
        'id': '123456789',
        'content': 'Test message',
        'senderId': 12345,
        'senderName': 'Test User',
        'timestamp': 1640995200000,
        'type': 'text',
      };

      // Act
      final message = ChatMessageModel.fromJson(json);

      // Assert
      expect(message.id, equals('123456789'));
      expect(message.content, equals('Test message'));
      expect(message.senderId, equals(12345));
      expect(message.senderName, equals('Test User'));
      expect(message.timestamp, equals(1640995200000));
      expect(message.type, equals(ChatMessageType.text));
      expect(message.senderAvatar, isNull);
      expect(message.roomId, isNull);
      expect(message.extra, isNull);
    });

    test('should handle unknown message type gracefully', () {
      // Arrange
      final json = {
        'id': '123456789',
        'content': 'Test message',
        'senderId': 12345,
        'senderName': 'Test User',
        'timestamp': 1640995200000,
        'type': 'unknown_type',
      };

      // Act
      final message = ChatMessageModel.fromJson(json);

      // Assert
      expect(message.type, equals(ChatMessageType.text)); // Should default to text
    });
  });

  group('ChatMessageType Tests', () {
    test('should have correct enum values', () {
      expect(ChatMessageType.text.name, equals('text'));
      expect(ChatMessageType.system.name, equals('system'));
      expect(ChatMessageType.notification.name, equals('notification'));
    });
  });

  group('ChatMessageModelExtension Tests', () {
    test('should correctly identify message types', () {
      // Arrange
      final textMessage = ChatMessageModel.createTextMessage(
        content: 'Hello',
        senderId: 123,
        senderName: 'User',
      );

      final systemMessage = ChatMessageModel.createSystemMessage(
        content: 'System message',
      );

      final notificationMessage = ChatMessageModel(
        id: '123',
        content: 'Notification',
        senderId: 0,
        senderName: 'System',
        timestamp: DateTime.now().millisecondsSinceEpoch,
        type: ChatMessageType.notification,
      );

      // Assert
      expect(textMessage.isTextMessage, isTrue);
      expect(textMessage.isSystemMessage, isFalse);
      expect(textMessage.isNotificationMessage, isFalse);

      expect(systemMessage.isTextMessage, isFalse);
      expect(systemMessage.isSystemMessage, isTrue);
      expect(systemMessage.isNotificationMessage, isFalse);

      expect(notificationMessage.isTextMessage, isFalse);
      expect(notificationMessage.isSystemMessage, isFalse);
      expect(notificationMessage.isNotificationMessage, isTrue);
    });
  });
}
